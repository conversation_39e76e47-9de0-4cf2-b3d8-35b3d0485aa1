Article

# Sustain AI: A Multi-Modal Deep Learning Framework for Carbon Footprint Reduction in Industrial Manufacturing

Manal Alghieth $\textcircled{1}$

Academic Editors: <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>

Received: 20 March 2025   
Revised: 17 April 2025   
Accepted: 29 April 2025   
Published: 2 May 2025

Citation: <PERSON><PERSON><PERSON><PERSON>, M. Sustain AI: A   
Multi-Modal Deep Learning   
Framework for Carbon Footprint   
Reduction in Industrial   
Manufacturing. Sustainability 2025, 17,   
4134. https://doi.org/10.3390/   
su17094134

Copyright: $\textcircled { \mathbb { C } } 2 0 2 5$ by the author. Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (https://creativecommons.org/ licenses/by/4.0/).

Department of Information Technology, College of Computer, Qassim University, Buraydah 51452, Saudi Arabia; <EMAIL>

Abstract: The growing energy demands and increasing environmental concerns in industrial manufacturing necessitate innovative solutions to reduce fuel consumption and lower carbon emissions. This paper presents Sustain AI, a multi-modal deep learning framework that integrates Convolutional Neural Networks (CNNs) for defect detection, Recurrent Neural Networks (RNNs) for predictive energy consumption modeling, and Reinforcement Learning (RL) for dynamic energy optimization to enhance industrial sustainability. The framework employs IoT-based real-time monitoring and AI-driven supply chain optimization to optimize energy use. Experimental results demonstrate that Sustain AI achieves an $1 8 . 7 5 \%$ reduction in industrial energy consumption and a $2 0 \%$ decrease in $\mathrm { C O } _ { 2 }$ emissions through AI-driven processes and scheduling optimizations. Additionally, waste heat recovery efficiency improved by $2 5 \%$ , and smart HVAC systems reduced energy waste by $1 8 \%$ . The CNN-based defect detection model enhanced material efficiency by increasing defect identification accuracy by $4 2 . 8 \%$ , leading to lower material waste and improved production efficiency. The proposed framework also ensures economic feasibility, with a $1 7 . 2 \%$ reduction in operational costs. Sustain AI is scalable, adaptable, and fully compatible with Industry 4.0 requirements, making it a viable solution for sustainable industrial practices. Future extensions include enhancing adaptive decision-making with deep RL techniques and incorporating blockchain-based traceability for secure and transparent energy management. These findings indicate that AI-powered industrial ecosystems can achieve carbon neutrality and enhanced energy efficiency through intelligent optimization strategies.

Keywords: Artificial Intelligence (AI); carbon footprint reduction; industrial sustainability; energy efficiency; Industry 4.0

# 1. Introduction

Global energy consumption and carbon emissions have increased by a great deal, further complicating the problem of climate change and environmental degradation, owing to the industrialization and rapid expansion of manufacturing processes. It is industrial operations that contribute the most to greenhouse gas, especially in the steel, cement, and logistics industries. Increasingly, industries are under the regulatory and sustainability spotlight as such frameworks and global sustainability initiatives as the Paris Agreement place tighter and tighter carbon reduction targets on them [1,2] and are required to adopt innovative, energy-efficient solutions to reduce their environmental footprint. In response to these demands, Artificial Intelligence (AI) is emerging as a transformative technology that can solve industrial energy consumption, waste management, and most automated processes [3,4].

Applications of AI and machine learning have recently proven to bring valuable contributions to carbon footprint reduction challenges through intelligent process optimization and predictive analytics. The implementation of such AI-driven models has been successful for the forecasting of carbon emissions, and the scenario of hindcasting seems to reflect that industries should take proactive steps to mitigate their environmental impact by predicting future carbon emissions based on historical data and real-time operational metrics. This, however, is not the only way that AI has been deployed in order to reduce transportation emissions; AI-powered supply chain optimization has also been able to reduce transportation emissions by improving route planning and reducing fuel consumption. Much achievement has also been made in energy efficiency with deep learning-based systems for waste heat recovery systems, combined with deep learning techniques, to reutilize surplus heat produced in industrial processes [5]. Nevertheless, the integration of multi-modal information fusion in industrial manufacturing systems is still an unsolved challenge, which is mainly because industrial data processing in real time and achieving seamless interoperability between different AI-driven subsystems [6,7] is so complex.

Since data sources are heterogeneous, they constitute a major bottleneck in AI-based industrial optimization. The data in industrial manufacturing environments are multisource and multi-modal, such as sensor readings from Internet of Things (IoT) devices, realtime environmental factors (temperature, humidity), operational logs, energy consumption records, and production efficiency indicators. Actionable insights are still very hard to extract—from integrating and processing these distinct data streams to the end—hence the need for sophisticated deep learning models for multi-modal information fusion [8,9]. The other critical limitation relies on current energy optimization models based on AI, which are not very flexible for dynamic industrial settings. A large portion of existing AI solutions has come up with static optimization methods without considering changes in real-time energy demand, which results in inefficient resource use (suboptimal allocation) and a great loss of energy [10,11]. Therefore, our need for an AI system that houses an adaptive learning mechanism, such as reinforcement learning, to update energy management strategies progressively as industrial conditions evolve is in high demand.

Scalability is another important factor to be considered in the deployment of AI-driven industrial optimization frameworks. Many existing solutions have been tested only in controlled environments or in restricted industrial setups and are not generalized to other manufacturing domains like automotive, steel, cement production, and logistics [12,13]. To keep the AI-powered optimization models and, in particular, AI components in the models robust and effective, they need to be extensively validated and adapted to cross domains of industrial applications. It is also known that one of the major hindrances to AI adoption on the general level in the industrial setting is the absence of transparency in the decision-making processes. However, most deep learning-based optimization models are ‘black box’ systems that are not interpretable or trustworthy by industrial operators and stakeholders as AI-based recommendations [14,15]. The capability to explain the decisions made by an AI system in industrial environments is crucial to promote greater user adoption and compliance with the regulations in this field, where operational decisions are directly related to financial and ecological consequences.

Thus, to address these challenges, this study presents Sustain AI’s advanced multimodal deep learning framework to optimize energy usage, improve sustainability, and reduce carbon emissions in industrial manufacturing. Industrial sustainability is achieved by Sustain AI as it solves the problem with a combination of AI-driven predictive analytics, real-time monitoring, reinforcement learning-based decision-making, and smart scheduling algorithms [16]. Unlike existing AI-based energy optimization approaches, the proposed framework leverages a number of multiple heterogeneous data sources, for example, IoT sensor data [17], historical energy consumption records [18], or real-time industrial logs to produce highly precise [19], adaptive energy optimization strategies [20], which are much more precise than what could be obtained solely from these conventional AI-powered approaches [21,22]. Sustain AI increases its energy consumption continually in response to production demand and external environmental demand, changing Sustain AI’s decision-making process through reinforcement learning. The model also uses Explainable AI (XAI) techniques to make AI-driven recommendations more interpretable and transparent to industrial operators so they can easily integrate these recommendations into existing workflows.

As global industrialization intensifies, the urgency to adopt AI-powered solutions for carbon mitigation becomes more evident. The IPCC’s Sixth Assessment Report emphasizes that industry remains a major contributor to global $\mathrm { C O } _ { 2 }$ emissions, responsible for approximately $2 4 \%$ of direct emissions, and highlights the need for transformative technologies to meet mitigation targets by the mid-century [23,24]. The importance of regulatory alignment is equally underscored by emerging mechanisms such as the European Union’s Carbon Border Adjustment Mechanism (CBAM), which imposes carbon pricing on imports to promote low-emission production across global supply chains [25]. At the operational level, empirical research has shown that energy efficiency and renewable integration play a pivotal role in reducing industrial carbon output, with AI acting as a strategic enabler of these optimizations [26]. Sustain AI is, therefore, designed not only to provide intelligent, data-driven optimization of energy and defect processes but also to ensure compliance with evolving international standards such as ISO 50001 and CBAM [26], aligning industrial sustainability efforts with global environmental governance frameworks.

The goal of this PhD research is to fill this gap with the development of a deep learning framework that is energy efficient, scalable, and industry-adaptive. The proposed model not only improves some of the energy efficiency and emission reduction efforts but largely introduces a novel, scalable, AI-driven infrastructure that is applicable to more than one industry. Sustain AI integrates these emerging deep learning, reinforcement learning, and IoT-based monitoring systems to a degree where it is a step forward in the realization of truly sustainable, intelligent industrial manufacturing ecosystems. The objectives of this study are mainly the following:

To create a multi-modal AI framework that combines the real-time sensor data from implemented IoT, predictive analytics, and deep learning for the purpose of industrial energy optimization [7,17]. To develop AI-driven process scheduling and smart energy management to reduce energy wastage and $\mathrm { C O } _ { 2 }$ emissions [18,19].   
. To build accurate predictions of carbon footprints based on the integration of real-time monitoring, machine learning, and knowledge from the domain [5,11].   
. To scale and generalize the proposed system to other industrial sectors such as steel, cement, and logistics [13,20]. The key contributions of this research are as follows:   
1. Multi-Modal Deep Learning Integration: To do this, we introduce a new AI-driven multi-modal information fusion framework for the IoT middleware (and sensor data) combined with deep learning-based forecasting for improving energy efficiency [2,6].   
2. AI-Powered Carbon Footprint Optimization: Reinforcement learning-based adaptive decision-making [10,18] is employed by the Sustain AI framework in order to optimize industrial carbon emissions.   
3. Real-Time Smart Scheduling and Predictive Control: In the proposed system, process scheduling and waste heat recovery systems are dynamic and based on AI-driven process scheduling and predictive analytics [9,20].   
4. Scalability and Cross-Industry Validation: This is tested in multiple industrial domains, thus allowing for real-world implementation in steel, cement, logistics, and automotive industries [15,16].   
5. Explainable AI for Industrial Applications: The model contains interpretable AI mechanisms to allow industrial operators to gain explainable decision-making insights for trust and transparency [14,19].

Unlike existing frameworks, such as ForestAdvisor, which focus primarily on forestry management and carbon policy optimization through multi-modal decision systems, Sustain AI is uniquely designed for industrial-scale implementation across sectors like steel, cement, logistics, and automotive. While ForestAdvisor leverages multi-modal AI for high-level policy decisions, Sustain AI dives deep into real-time process-level optimization using a tightly integrated architecture of CNNs for defect detection, RNNs for predictive energy modeling, and Reinforcement Learning (RL) for adaptive control of dynamic energy consumption scenarios. Moreover, compared to platforms like DeepMind’s industrial agents, which focus on isolated factory optimizations using RL, Sustain AI extends the capability by incorporating heterogeneous industrial data streams (e.g., IoT, HVAC logs, supply chain metrics) into a unified deep learning pipeline. This end-to-end framework enables simultaneous optimization of energy efficiency, $\mathrm { C O } _ { 2 }$ reduction, and cost while also ensuring interpretability through explainable AI mechanisms—making it more practical and transparent for industrial stakeholders.

The rest of this paper is organized as follows: Section 2 shows the systematic literature review, and Section 3 provides the methodology, i.e., data acquisition, preprocessing, and model development. In Section 4, the Sustain AI architecture and its main components are proposed. Section 5 presents experimental results and discussions on the effect of AI-driven optimization of industrial energy efficiency and carbon footprint reduction. The study concludes in Section 6 with future research directions.

# 2. Literature Review

In recent years, AI and multi-modal deep learning integration has been observed in industrial manufacturing. Researchers have explored different industrial domains [1–3] regarding various AI-driven solutions to optimize energy efficiency, reduce carbon emissions, and improve sustainability. In this section, we briefly review the contributions of state-ofthe-art research on AI-driven industrial process optimization in predicting carbon emission, waste heat recovery, supply chain optimization, and industrial monitoring systems.

# 2.1. Carbon Emission Prediction and Reduction

It is of great importance to accurately forecast emissions of carbon so that effective mitigation strategies can be implemented. In a bidirectional Long Short-Term Memory (BiLSTM) model, Li et al. [1] suggested a combination method of text-based and data-driven multi-modal information to predict industrial carbon emissions. Deep learning analysis of heterogeneous data sources was demonstrated in their study for real-time emission forecasting with high accuracy. Ji et al. [2] also presented a multi-modal decision-making framework (ForestAdvisor) that combines carbon reduction strategies and economic viability. The approach showed the potential of machine learning in carbon management policy optimization for large-scale industrial operations.

AI has also been put to direct use to achieve emission reductions. Wang et al. [3] developed a multi-modal machine learning framework for low-carbon aeration in wastewater treatment plants to achieve significant reductions in energy consumption and carbon emissions. According to their results, AI plays a critical role in environmental sustainability, especially in industrial waste management. Additionally, Song et al. [6] used AI applications in engineering design by showing that multi-modal data fusion can improve sustainability by reducing process inefficiencies.

# 2.2. AI-Driven Energy Efficiency and Waste Heat Recovery

Keeping energy efficiency optimization as the first and most prominent goal of AIdriven industrial applications is still the top priority. The models for monitoring and optimizing energy consumption in real time have been developed by researchers using AI. In an effort to minimize energy wastage, AI has been shown to enhance sustainability through predictions of industrial steam consumption, as introduced by Liu et al. [12]. In a similar way, Zhang et al. [9] suggested an energy consumption lightweight AI framework for predicting solar irradiance to optimize renewable energy sectors.

Waste heat recovery is one area of research that has applied AI’s prowess to maximize energy reuse in industrial plants. Mantis [5] developed a generalizable electrocatalyst design framework based on multi-modal AI towards improving energy efficiency in heat recovery processes. The findings of their study were that the carbon footprint of manufacturing industries can be reduced when the performance of thermal energy storage systems is maximized by the use of AI-driven optimization. Zhao et al. [7] also further extended multi-scale hybrid attention networks for heat dissipation monitoring in laser-induced thermal crack processing, which yields significant energy conservation.

# 2.3. Supply Chain Optimization for Carbon Reduction

Optimization of the supply chain logistics is the next critical strategy to reduce industrial carbon emissions. Bello et al. [21] propose a dynamic AI-powered logistics platform where fuel consumption is reduced by optimization of transportation routes using several modal machine learning algorithms. Their study showed a $1 0 \%$ reduction in transportationrelated carbon emissions (which can be considered proof of the fact that AI has the potential to aid in sustainable supply chain management). Another model presented by Boumaraf et al. [11] is an optimized model for flare performance analysis based on temporal standard deviation enhancements for a reduction in industrial transportation gas emissions.

The predictive maintenance in supply chain operations is also enhanced with the help of AI. Hao et al. [15] proposed a multi-modal forecasting model for tool wear monitoring in order to make maximum use of resources in the manufacturing chain. Their model had a significant reduction in downtime and material waste, fitting the sustainability objective. Based on these efforts, Almujally et al. [14] improved the logistic efficiency and lowered emissions using remote sensing data within AI-driven logistics.

# 2.4. AI-Enabled Industrial Monitoring and Environmental Sustainability

Industrial sustainability can also be achieved with the aid of AI-powered systems that directly monitor energy consumption, emissions, and the operational efficiency of plants and machines in real time. Based on the paper, Wang et al. [4] developed a multi-modal deep learning framework to facilitate environmental monitoring in mining enterprises, enhancing decision-making and reducing environmental hazards with the aid of AI. The system they used incorporated sensor data and satellite imagery, together with predictive modeling, to improve industrial compliance with environmental regulations.

Rehman et al. [13] tackle the advancements in multi-modal intelligent sensing and how AI can help in energy usage optimization through adaptive control mechanisms in the context of smart manufacturing. The emphasis of their study was to combine AI-enabled industrial IoT systems for energy monitoring and predictive maintenance in real time. Wang et al. [8] also showed the role that AI can play in enzymatic active site annotation as a means of bioengineering and environmental sustainability.

# 2.5. Challenges and Future Research Directions

Although there have been a considerable number of advances, there still remain many challenges to incorporating AI for industrial sustainability. Despite the progress made in real-time AI applications, multi-modal data fusion remains a challenge due to the complexity of the problem [10]. Additionally, the interpretability and explainability of AI-driven decisions are a core issue in high-risk industrial environments where human oversight is critical [16].

Future work should develop reinforcement learning-based adaptive energy optimization; that is, AI adjusts industrial processes automatically to match occupancy variations in energy demands [17]. Additionally, the application of blockchain technology in transparent and secure carbon footprint tracking is also an area that is gearing up towards integration into AI-driven industrial sustainability frameworks [22]. Finally, we will need to extend AI-driven optimization models to more industrial domains spanning the pharmaceutical and chemical industries in order for sustainability to be adopted widely [23].

# 2.6. Summary of Literature Review

A particular characteristic of AI is that it has been applied to industrial sustainability applications: prediction of carbon emissions, energy efficiency optimization, supply chain management, and environmental monitoring. Recent studies have contributed key points, which are summarized in Table 1.

Table 1. Summary of key contributions in AI-driven industrial sustainability.   

<html><body><table><tr><td>Study</td><td>Key Contribution</td><td>Industry Application</td></tr><tr><td>[1]</td><td>AI-based carbon emission prediction using BiLSTM</td><td> Industrial emissions forecasting</td></tr><tr><td>[2]</td><td>Multi-modal AI for forest decision-making</td><td>Carbon management in forestry</td></tr><tr><td>[3]</td><td>AI-guided low-carbonwastewater treatment</td><td>Wastewater energy optimization</td></tr><tr><td>[4]</td><td>Deep learning for industrial steam consumption</td><td>Manufacturing energy efficiency</td></tr><tr><td>[5]</td><td>AI-driven logistics optimization</td><td>Supply chain emissions reduction</td></tr><tr><td>[6]</td><td>Multi-modal AI for industrial IoT monitoring</td><td>Smart manufacturing efficiency</td></tr><tr><td>[7]</td><td>AI-powered predictive maintenance</td><td>Tool wear reduction in factories</td></tr><tr><td>[16]</td><td>CNN-based soil erosion detection</td><td>Environmental sustainability</td></tr></table></body></html>

In total, these findings all tend to support the transformative potential of AI towards green industrial practices. The following section presents the proposed Sustain AI framework, which offers a multi-modal AI-powered energy optimization system based on these advances.

Existing research in industrial AI primarily falls into three categories: static singlemodel approaches, digital twin-based simulations, and multi-modal deep learning systems. Static models, such as time-series forecasting using ARIMA or standalone LSTM, offer simplicity but lack adaptability to real-time operational changes. Digital twin platforms simulate industrial processes but often require extensive domain-specific customization and typically lack adaptive learning capabilities. While useful for what-if analyses, they are computationally intensive and limited in dynamic optimization. On the other hand, current multi-modal deep learning frameworks often focus on isolated tasks—such as defect detection or energy forecasting—without a unified, adaptive decision-making layer. Sustain AI bridges this gap by integrating multiple heterogeneous data streams (e.g., visual, temporal, ERP logs) and incorporating reinforcement learning for continuous process optimization, offering a scalable, end-to-end solution not seen in prior models. This comparative understanding underscores the theoretical and practical originality of Sustain AI in combining prediction, control, and real-time adaptability within a single intelligent framework.

Despite recent advances, several critical gaps persist in the application of AI for industrial sustainability. Most existing studies focus on isolated techniques—such as single time-series models for energy forecasting or standalone CNNs for defect detection— without addressing the complexity of multi-modal data integration in real-time industrial environments. Furthermore, while digital twin technologies offer promising simulation capabilities, they often lack adaptive learning components like reinforcement learning that can respond to dynamic operational changes. Current AI solutions are also typically sector-specific, limiting their cross-domain scalability. Sustain AI addresses these gaps by introducing a unified, multi-modal deep learning framework that not only fuses heterogeneous data sources (e.g., sensor data, ERP logs, HVAC performance) but also integrates adaptive control through RL and interpretability via explainable AI. This approach provides a more holistic, responsive, and generalizable solution to industrial sustainability challenges, thereby advancing both the theoretical understanding and practical deployment of intelligent manufacturing systems.

# 3. Methodology

This section describes how, in this research, we optimize industrial processes using AI-based methods. Data acquisition, preprocessing, model development, and evaluation phases are covered within the methodology, which leads to a systematic and reproducible research process. The main goal is to use AI-based optimization methods to increase manufacturing plant efficiency and reduce the carbon footprint in industrial manufacturing.

# 3.1. Dataset Collection

This research used a dataset from many industrial manufacturing sites that are related to energy consumption, $\mathrm { C O } _ { 2 }$ emissions, and operational efficiency. Iota, available via IoT-enabled sensors and Enterprise Resource Planning (ERP) systems and collected on publicly continuously monitored environmental datasets, were utilized to collect data.

Sustain AI was trained and evaluated using publicly available industrial datasets, including the UCI Energy Efficiency Dataset, ASHRAE Energy Prediction Dataset, and the Industrial IoT Energy Consumption Dataset, combined with real-time IoT sensor data collected from smart manufacturing environments. It comprises time-series energy consumption logs, HVAC performance records, carbon emission reports, and operational indicators in multiple industries, e.g., steel, cement, and logistics. All the data were preprocessed by removing inconsistencies, normalizing the values, and ensuring completeness for use in AI-based optimization models.

This research uses a dataset consisting of various industrial data from different manufacturing plants in terms of energy consumption, carbon emissions, and operational efficiency. The data sources are IoT-enabled sensors, Enterprise Resource Planning (ERP) systems, and publicly available industrial reports. The dataset covers different sectors, including steel manufacturing, cement production, and logistics, and hence, covers a large number of energy-intensive industries. The energy logs are time-series, real-time HVAC performance data, waste heat recovery efficiency, carbon capture statistics, and AI-driven supply chain optimizations. Each record is timestamped and categorized by operational conditions such that detailed trend analysis is possible. Moreover, the dataset includes external environmental factors, including temperature and humidity, that affect industrial energy consumption. Data cleaning, normalization, and missing value imputation were conducted to make the data reliable. This dataset is the basis for training AI models to efficiently decrease the overall carbon footprint of industrial processes.

# 3.2. Dataset Description

This research uses various industrial parameters collected from several manufacturing plants centered on energy consumption, carbon emissions, and operational efficiency. The dataset attributes are listed in the following Table 2.

Table 2. Overview of dataset attributes.   

<html><body><table><tr><td>Attribute</td><td>Description</td></tr><tr><td> Energy Consumption Logs</td><td>Time-series energy consumption data recorded from industrial equipment and HVAC systems.</td></tr><tr><td>Carbon Emissions Data</td><td>CO2 emission levels tracked from various production units over time.</td></tr><tr><td>Operational Efficiency</td><td>Data on production efficiency, equipment performance,and downtime analysis.</td></tr><tr><td>HVACPerformance</td><td>Temperature, ventilation,and air conditioning data collected from smart manufacturing plants.</td></tr><tr><td>Waste Heat Recovery Efficiency</td><td>Metrics related to heat recovery systems,including energy reuse and loss reductions.</td></tr><tr><td>Supply Chain Optimization</td><td>AI-driven logistics data to reduce fuel consumption and optimize delivery routes.</td></tr><tr><td>Carbon Capture Statistics</td><td>Measurements of AI-enhanced carbon capture performance in industrial plants.</td></tr><tr><td>External Environmental Factors</td><td>Temperature, humidity,and other external conditions impacting energy consumption.</td></tr></table></body></html>

# 4. Proposed Model: Sustain AI

This research advances the field of industrial sustainability by proposing a novel multi-modal deep reinforcement learning framework—Sustain AI—that addresses key theoretical gaps in energy system optimization and smart manufacturing. Unlike prior works that treat data sources in isolation or rely on static models, Sustain AI introduces an adaptive, real-time decision-making architecture that integrates heterogeneous data streams (e.g., image, time-series, categorical) using a unified deep learning pipeline. The innovation lies in its attention-based fusion mechanism that dynamically weighs each modality based on its contextual relevance to energy optimization and defect detection tasks. Moreover, Sustain AI leverages a composite reward structure within Proximal Policy Optimization (PPO) that encodes real-world operational constraints such as safety margins, production requirements, and emissions thresholds. This marks a methodological leap from rule-based systems and single-modal learning, offering a scalable framework capable of handling high-dimensional, noisy, and temporally misaligned data common in industrial environments. In terms of theoretical contributions, Sustain AI bridges the gap between predictive modeling and prescriptive control by combining long-horizon sequence learning (via RNN/Transformer) with adaptive decision-making (via RL) in one cohesive system. This enables not just forecasting energy demand or emissions but actively minimizing them through policy learning. Compared to existing smart manufacturing solutions, Sustain AI introduces a system-level learning approach that generalizes across multiple sectors and adapts continuously, making it a significant step forward in the theory and practice of intelligent, carbon-aware manufacturing ecosystems.

# 4.1. Overview

Sustain AI is a multi-modal deep learning framework that aims to minimize the energy footprint and carbon footprint in industrial manufacturing. The framework uses machine learning, reinforcement learning, and IoT-based monitoring to adjust dynamically energy-intensive processes while maintaining operational efficiency. Thus, it presents a data-driven methodology for waste reduction, heating and cooling system optimization, and logistics in sustainable manufacturing. While the study emphasizes energy consumption and carbon emission reductions, broader sustainability governance dimensions such as ESG (Environmental, Social, and Governance) and SDGs (Sustainable Development Goals) are critical to contextualizing the real-world value. Sustain AI can directly contribute to SDG 9 (Industry, Innovation and Infrastructure), SDG 12 (Responsible Consumption and Production), and SDG 13 (Climate Action) by enabling data-driven, low-emission manufacturing strategies. In terms of ESG alignment, the system enhances environmental reporting through real-time carbon tracking, improves operational transparency via explainable AI, and supports governance by allowing integration with compliance frameworks. To ensure regulatory compatibility, Sustain AI is designed to align with ISO 50001 (Energy Management Systems), which defines requirements for energy performance monitoring and is adaptable to emerging carbon regulation frameworks such as the EU Carbon Border Adjustment Mechanism (CBAM). Future versions of Sustain AI will incorporate KPI dashboards that track metrics like carbon intensity per unit output, energy use per production cycle, and compliance scoring—enabling industries to meet both internal ESG mandates and international climate regulations.

# 4.2. Architecture

Each layer of the architecture of Sustain AI follows a collaborative approach to collect, process, analyze, and optimize data related to energy consumption in the industrial environment. Sustain AI employs a hybrid multi-modal data fusion architecture designed to handle diverse industrial data types, including time-series sensor logs, image-based defect data, and categorical metadata from ERP systems. The fusion mechanism is based on a staged approach: first, data streams are independently processed using modality-specific encoders—1D CNNs and RNNs (LSTM layers) for time-series data, 2D CNNs for image data, and embedding layers for categorical features. These encoded representations are then temporally aligned using a shared timestamp reference and fused at the feature level using an attention-based concatenation module. This module dynamically weighs the importance of each modality based on the current context, allowing the model to focus on the most relevant data stream per task (e.g., energy anomaly prediction vs. material defect detection). For instances where precise alignment is challenging due to varying sampling rates, interpolation, and windowing techniques are used to synchronize inputs into fixed-length sequences. This architecture ensures that heterogeneous industrial data RaErVe EpWrocessed cohesively, enabling accurate and context-aware decisi1o0no-fm31aking in real time. The key components are in the following Figure 1:

![](images/df7c2794b4cab10db7dbeeca65da0cd1179923b10c85b37fd2186bc719f736d4.jpg)  
Fiigure 1. . Modoeldaerlchairtcechtiutrec. ture.

Data Acquisition Module: Real-time data collection from IoT sensors, industrial automation systems, and environmental monitoring stations.   
. Data Preprocessing Layer: Handle missing values, input the consistent values, remove inconsistencies, and normalize.   
• Deep Learning Optimization Module: Anomaly detection is also included via CNNs and for time-series forecasting of energy consumption by RNNs.   
• Reinforcement Learning (RL) Engine: Solves dynamic energy usage problems using DQN and PPO algorithms.   
. AI-Powered Decision Support System: It suggests real-time optimization and manages energy.   
. Cloud and Edge Computing Integration: It helps in distributed AI processing for industrial-scale deployment.

# 4.3. Mathematical Model

Sustain AI leverages a broad set of multi-modal data sources collected from smart industrial environments to enable holistic process optimization: (1) video data from highresolution inspection cameras used in visual defect detection processed through CNNs; (2) time-series sensor data from IoT-enabled devices tracking temperature, humidity, vibration, power usage, and machine load; (3) audio signals from acoustic sensors used to detect anomalous machine sounds for predictive maintenance; (4) ERP system logs and factory operation records detailing material flow, production scheduling, and shift performance; and (5) categorical metadata, such as equipment type, operational mode, or operator ID. Each data stream differs in frequency, format, and noise characteristics. To manage this heterogeneity, the preprocessing pipeline includes synchronized timestamp alignment using event triggers, data resampling for temporal normalization (e.g., aligning 30 FPS video frames with $1 0 \mathrm { H z }$ sensor data), and denoising techniques, such as wavelet transforms (for audio) and moving average filters (for time-series logs). After normalization and missing value handling, all modalities are encoded into a unified latent representation using modality-specific encoders (e.g., CNNs for images, LSTMs for sequences, and embeddings for categorical data). These representations are then fused using an attention-based multi-modal fusion layer that dynamically adjusts modality importance based on the target prediction task. This rigorous preprocessing and fusion strategy ensures that all input streams contribute meaningfully to Sustain AI’s optimization outcomes.

To address the limitations of existing multi-modal industrial AI systems, this study proposes a novel Adaptive Attention Fusion Module (AAFM) as a core theoretical contribution. Unlike conventional fusion strategies that use fixed-weight concatenation or late fusion heuristics, AAFM dynamically assigns modality-specific attention weights based on contextual relevance to the optimization task (e.g., energy forecasting vs. defect detection). This enables task-aware feature prioritization, allowing the system to selectively emphasize the most informative data stream (e.g., time-series sensor data during energy optimization, image features during quality inspection). The module also performs temporal alignment and interpolation across inputs with varying sampling rates, resolving a critical challenge in real-time industrial settings. Integrated within the Sustain AI pipeline, AAFM acts as the bridge between perception (CNN/RNN encoders) and decision-making (PPObased RL), enabling cohesive learning from asynchronous, heterogeneous inputs. This fusion mechanism not only improves prediction and control accuracy but also represents a methodological advancement by tightly coupling multi-modal representation learning with adaptive policy optimization—thereby contributing a systematic breakthrough to the field of industrial AI for sustainability.

To further understand the trade-off between energy efficiency and carbon reduction in the optimization objective, a sensitivity analysis was conducted on the weighting factor $\lambda$ . This factor balances the relative importance of minimizing energy consumption versus reducing $\mathrm { C O } _ { 2 }$ emissions. By varying $\lambda$ in the range of 0 to 1, it was observed that lower values (e.g., $\lambda = 0 . 2$ ) prioritize carbon reductions more aggressively, often leading to slight increases in total energy usage due to the selection of low-emission but less energy-efficient sources. Conversely, higher values (e.g., $\lambda = 0 . 8$ ) favor energy efficiency, sometimes at the cost of higher emissions. Optimal performance was found around $\lambda = 0 . 5$ , where both energy and emission metrics improved concurrently. This sensitivity analysis confirms that $\lambda$ is a critical hyperparameter in tuning Sustain AI’s performance to meet varying sustainability objectives and regulatory constraints in different industrial settings. The core objective of Sustain AI is to minimize energy consumption while maximizing efficiency. The optimization problem is formulated as follows:

$$
\underset { P , x } { \operatorname* { m i n } } \ : \ : \sum _ { i = 1 } ^ { N } E _ { i } ( x _ { i } ) + \lambda \sum _ { j = 1 } ^ { M } C _ { j } \left( x _ { j } \right)
$$

# where:

$E _ { i } ( x _ { i } )$ represents the energy consumption of industrial process $i$ as a function of operational parameters $x _ { i }$ .   
$C _ { j } \big ( x _ { j } \big )$ represents the carbon emissions from energy source $j$ .   
$\lambda$ is a weighting factor balancing energy efficiency and carbon footprint reduction.   
$N$ is the number of manufacturing processes.   
$M$ is the number of energy sources.

The optimization problem defined in Equation (1) aims to minimize a weighted sum of energy consumption and carbon emissions. While the formulation is generalizable across various industrial processes, it is important to analyze its tractability. Under typical assumptions where energy consumption functions $E _ { i } ( x )$ and emission functions $C _ { j } ( x )$ are smooth and continuous, the problem can be made convex by appropriately selecting or approximating these functions (e.g., quadratic or piecewise–linear energy-emission curves). Convexity ensures that local minima are also global minima, enabling reliable optimization. In the implementation of Sustain AI, gradient-based methods such as stochastic gradient descent (SGD) and Adam optimizer are used to find optimal parameters for the predictive models and RL-based policy updates. To guarantee convergence, learning rates are adaptively tuned, and convergence is checked based on the stability of the loss function and policy performance over successive epochs. Specifically, convergence criteria include the minimization of gradient norms below a set threshold and stabilization of cumulative reward in RL environments. This ensures both computational feasibility and real-time applicability in industrial settings.

# Constraints

Energy consumption and emissions must satisfy regulatory and operational constraints:

$$
P _ { m i n } \leq P _ { i } \leq P _ { m a x } , \forall i \in N
$$

$$
C _ { m i n } \leq C _ { j } \leq C _ { m a x } , \forall j \in M
$$

where:

$P _ { i }$ is the power consumed by process i.   
$C _ { j }$ is the carbon emissions generated by source $j$ .

# 4.4. Reinforcement Learning Formulation

In the enhanced reinforcement learning framework within Sustain AI, the design of the state space, action space, and reward function is carefully tailored to reflect real industrial conditions and constraints. The state space comprises a multi-dimensional vector representing the factory’s real-time operational environment. This includes variables such as real-time production output (units per hour), equipment load (measured in kilowatts or utilization percentage), ambient temperature and humidity levels, electricity price (USD per kWh), machine operational status (e.g., on, off, or failure codes), $\mathrm { C O } _ { 2 }$ emission rate, energy storage level (in percentage), current shift schedule (binary encoded), and time of day (to capture temporal demand patterns). These variables collectively capture the physical and economic dynamics that influence energy usage and process efficiency. The action space allows the agent to make decisions that directly impact energy consumption and operational performance. These actions include adjusting HVAC system setpoints, redistributing machine workloads, scheduling or delaying high-energy tasks, switching between energy sources (e.g., grid power, solar, or stored energy), and activating or pausing systems like carbon capture or waste heat recovery. Through these actions, the agent can dynamically respond to shifting operational demands and external conditions. The reward function is designed as a composite metric that balances energy efficiency, emission reductions, production quality, and safety compliance. It penalizes excessive energy use and emissions while rewarding adherence to production targets and safe equipment operation. Formally, the reward function integrates four main components: energy consumption, $\mathrm { C O } _ { 2 }$ emissions, deviation from optimal machine load or safety boundaries, and bonuses for staying within operational constraints or achieving efficiency goals. By optimizing this reward signal over time, the reinforcement learning agent learns to select actions that lead to optimal or near-optimal outcomes across diverse scenarios. This includes managing trade-offs between energy savings and production requirements while adhering to safety regulations and scheduling constraints. The design ensures that the learned strategies are not only efficient but also realistic and deployable in real-world industrial environments.

Sustain AI employs Reinforcement Learning (RL) to dynamically optimize industrial processes. The RL-based energy optimization is formulated as a Markov Decision Process (MDP) with the following parameters:

State Space (S): Includes current energy consumption, machine workload, and environmental conditions.   
Action Space $( A )$ : Set of process adjustments such as load balancing, heating/cooling optimization, and energy source switching.   
Reward Function $( R )$ : Measures efficiency improvements and emissions reduction. The RL optimization objective is as follows:

$$
\operatorname* { m a x } _ { \pi } \sum _ { t = 0 } ^ { T } \gamma ^ { t } R _ { t }
$$

where:

$\pi$ is the policy function mapping states to actions.   
$\gamma \in \left( 0 , 1 \right]$ is the discount factor for future rewards.   
$T$ is the optimization horizon.

Reward Function

While the current reward function effectively guides the RL agent toward reducing energy consumption and emissions, it adopts a relatively simplistic formulation. In future work, we plan to extend this to a shaped reward structure that incorporates operational constraints, such as maximum allowable temperature ranges, safety thresholds, and equipment wear rates. Composite rewards with penalty terms will allow the agent to balance optimization goals with real-world industrial safety and compliance requirements, improving the model’s realism and deployment readiness. The reward function is defined as:

$$
R _ { t } = \alpha ( E _ { \mathrm { b a s e l i n e } } - E _ { t } ) + \beta ( C _ { \mathrm { b a s e l i n e } } - C _ { t } ) - \delta L _ { t }
$$

where:

$E _ { t }$ and $C _ { t }$ are the current energy consumption and emissions.   
$L _ { t }$ is the operational load deviation cost.   
$\alpha , \beta ,$ and $\delta$ are weight coefficients.   
$R _ { t }$ is the reward at time $t$ used in the reinforcement learning objective.   
$E _ { b a s e l i n e }$ is the baseline (pre-optimization) energy consumption.   
$C _ { b a s e l i n e }$ is the baseline $\mathrm { C O } _ { 2 }$ emissions before optimization.

# 4.5. Cost Savings and Efficiency Improvement

The financial impact of Sustain AI is evaluated using the following cost reduction model:

$$
\operatorname* { m i n } _ { x } \ \sum _ { i = 1 } ^ { N } \Bigl ( C _ { i } ^ { \mathrm { e n e r g y } } ( x _ { i } ) + C _ { i } ^ { \mathrm { m a i n t e n a n c e } } ( x _ { i } ) \Bigr )
$$

where:

Cienergy(xi) is the cost of energy for process i.   
$C _ { i } ^ { \mathrm { m a i n t e n a n c e } } ( x _ { i } )$ represents machine wear and maintenance costs.

The total efficiency gain is calculated as:

$$
\eta = \frac { E _ { \mathrm { b a s e l i n e } } - E _ { \mathrm { o p t i m i z e d } } } { E _ { \mathrm { b a s e l i n e } } } \times 1 0 0 \
$$

where:

$\eta$ is the percentage efficiency gain.   
$E _ { \mathrm { b a s e l i n e } }$ is the energy consumption before optimization.   
Eoptimized is the energy usage after AI-based improvements.

# 4.6. Implementation Strategy

The deployment of Sustain AI follows a four-phase approach:

Phase 1: Pilot Deployment—Testing the AI system in selected industrial units. Phase 2: Model Training and Calibration—Training AI models with historical and real-time data. Phase 3: Full-Scale Implementation—Deploying the model across multiple industrial plants. . Phase 4: Continuous Learning and Optimization—Refining strategies through realtime feedback.

Sustain AI is an advanced AI-driven solution to optimize industrial energy usage and mitigate emissions. Such a framework of sustaining manufacturing using deep learning, reinforcement learning, and IoT monitoring can be achieved by integrating Sustain AI. The aim of the proposed system is to be integrated with the solutions of Industry 4.0, which will help industries move towards energy-efficient and eco-friendly operations.

# 4.7. Evaluation Metrics

To evaluate Sustain AI’s effectiveness in solving industrial energy efficiency optimization and concurrent carbon footprint reductions, we define the following key metrics.

# 4.7.1. Energy Efficiency Improvement

The reduction in energy consumption is a primary indicator of the model’s success. It is calculated as:

$$
\eta _ { e n e r g y } = \frac { E _ { \mathrm { b a s e l i n e } } - E _ { \mathrm { o p t i m i z e d } } } { E _ { \mathrm { b a s e l i n e } } } \times 1 0 0 \
$$

where:

$E _ { \mathrm { b a s e l i n e } }$ is the total energy consumption before AI optimization.   
$E _ { \mathrm { o p t i m i z e d } }$ is the energy consumption after implementing Sustain AI.   
ηenergy represents the percentage reduction in energy usage.

# 4.7.2. Carbon Footprint Reduction

To quantify the reduction in $\mathrm { C O } _ { 2 }$ emissions achieved by Sustain AI, we use:

$$
\eta _ { c a r b o n } = \frac { C _ { \mathrm { b a s e l i n e } } - C _ { \mathrm { o p t i m i z e d } } } { C _ { \mathrm { b a s e l i n e } } } \times 1 0 0 \
$$

where:

Cbaseline is the carbon emissions before optimization.   
Coptimized is the carbon emissions after optimization.   
ηcarbon represents the percentage reduction in $\mathrm { C O } _ { 2 }$ emissions.

# 4.7.3. Operational Cost Savings

The financial impact of AI-driven energy optimization is measured as:

$$
\eta _ { c o s t } = \frac { O _ { \mathrm { b a s e l i n e } } - O _ { \mathrm { o p t i m i z e d } } } { O _ { \mathrm { b a s e l i n e } } } \times 1 0 0 \
$$

where:

Obaseline represents operational costs before AI intervention.   
$O _ { \mathrm { o p t i m i z e d } }$ represents operational costs after optimization.   
$\eta _ { c o s t }$ denotes the percentage of cost savings.

# 4.7.4. System Response Time

The efficiency of AI-driven decision-making is measured in terms of response time:

$$
T _ { \mathrm { r e s p o n s e } } = \frac { 1 } { N } { \sum _ { i = 1 } ^ { N } } { \left( T _ { \mathrm { d e c i s i o n } } - T _ { \mathrm { i n p u t } } \right) }
$$

where:

$T _ { \mathrm { i n p u t } }$ is the timestamp when data are received by the AI system.   
Tdecision is the timestamp when the AI model outputs a decision.   
$N$ is the number of decision instances.   
Tresponse represents the average system response time.

# 4.7.5. Model Accuracy

The predictive performance of Sustain AI’s deep learning models is evaluated using the Mean Absolute Error (MAE) and Root Mean Square Error (RMSE):

$$
M A E = \frac { 1 } { N } \sum _ { i = 1 } ^ { N } \lvert y _ { i } - \hat { y } _ { i } \rvert
$$

$$
R M S E = \sqrt { \frac { 1 } { N } { \sum _ { i = 1 } ^ { N } } ( y _ { i } - \hat { y } _ { i } ) ^ { 2 } }
$$

where:

$y _ { i }$ is the actual observed value.   
$\hat { y } _ { i }$ is the predicted value from the model.   
$N$ is the total number of observations.

# 4.7.6. Reinforcement Learning Convergence

To assess the stability of the RL policy across different industries, we monitored reward convergence trends over multiple training runs. On average, the policy achieved stable performance after approximately 1500 to 2000 episodes for logistics and automotive sectors, and around 2500 episodes for more complex environments like steel and cement production. While convergence behavior was consistent, convergence speed varied based on the complexity and volatility of operational conditions. This highlights the importance of domain-specific calibration and reward shaping to accelerate training in diverse industrial settings. The performance of Reinforcement Learning (RL) algorithms in Sustain AI is evaluated based on reward convergence:

$$
\operatorname* { l i m } _ { t  \infty } R _ { t } = R ^ { * }
$$

where:

$R _ { t }$ is the cumulative reward at time step $t$ .   
$R ^ { * }$ is the optimal reward value when the policy converges.

# 4.7.7. Scalability and Adaptability

The scalability of Sustain AI is measured using the system’s ability to handle increased data loads efficiently:

$$
S _ { \mathrm { s c a l a b i l i t y } } = { \frac { P _ { \mathrm { n e w } } - P _ { \mathrm { b a s e l i n e } } } { P _ { \mathrm { b a s e l i n e } } } } \times 1 0 0 \
$$

where:

$P _ { \mathrm { b a s e l i n e } }$ is the system performance with the initial dataset size.   
$P _ { \mathrm { n e w } }$ is the system performance after increasing data volume.   
Sscalability represents the scalability percentage.

The evaluation metrics presented are the most comprehensive ways to assess Sustain AI’s efficiency, environmental impact, financial savings, and computational performance. These quantitative measures guarantee the robustness and reliability of the proposed model in minimizing industrial energy consumption and reducing carbon emissions.

# Training Configuration and Experimental Setup:

To ensure reproducibility and transparency, the training configuration of the Sustain AI framework is provided here.

The deep learning components were trained using a batch size of 64, a learning rate of 0.001, and the Adam optimizer. For RNN-based forecasting, we used two LSTM layers with 128 hidden units each and a dropout rate of 0.2 to prevent overfitting. The CNN module for defect detection consisted of three convolutional layers (filter sizes: 32, 64, 128), each followed by ReLU activation and max-pooling layers. The reinforcement learning component used Proximal Policy Optimization (PPO) with a discount factor (γ) of 0.95 and policy updates every 2048 steps. Each model was trained over 50 epochs, with early stopping applied if the validation loss did not improve for five consecutive epochs. All experiments were conducted on a system equipped with an NVIDIA RTX 3090 GPU (24 GB VRAM), 128 GB RAM, and an Intel Xeon Gold 6226R CPU. The average training time per module was approximately $2 \mathrm { h }$ for the CNN, $3 . 5 \mathrm { h }$ for the RNN-based forecasting, and $6 \mathrm { { h } }$ for the reinforcement learning agent. The complete pipeline training (including fusion layers) took approximately $1 2 \mathrm { h }$ . All models were implemented using PyTorch 2.0 and trained using CUDA 11.8.

# 5. Results and Discussion

In this section, the results gained from implementing Sustain AI are presented, and the consequences of AI-based optimization on industrial energy efficiency and carbon footprint are discussed. It is evaluated in terms of multiple performance metrics, including (reduced) energy consumption, (reduced) carbon footprint, operational cost savings, model accuracy, and system efficiency. To support the reported $4 2 . 8 \%$ improvement in defect detection accuracy, we used a dataset comprising over 38,000 annotated samples collected from three smart manufacturing lines in the steel and automotive sectors. The defect images were acquired through high-resolution camera systems positioned along the production lines, capturing real-time visual data of materials in motion. Annotations were manually labeled by domain experts using bounding boxes and classification tags, covering common industrial defects such as surface cracks, porosity, welding gaps, deformation, and foreign material intrusion. The dataset was balanced to avoid class bias and split into training $( 7 0 \% )$ , validation $( 1 5 \% )$ , and test $( 1 5 \% )$ subsets. The performance improvement was measured against a baseline manual inspection process, where the model achieved a higher true positive rate with significantly fewer missed defects. The $4 2 . 8 \%$ reduction in defect rate was calculated based on a comparative analysis of defective output before and after the deployment of the CNN model across two months of production logs under consistent process parameters and inspection criteria. From a sustainability standpoint, this improvement translated into substantial material savings and line efficiency gains. Fewer defective products reduced raw material waste, rework cycles, and quality inspection delays, contributing directly to energy conservation and cost savings. While integrating automated defect detection systems may introduce marginal increases in power consumption (due to image processing hardware and GPU computation), this overhead is minimal compared to the energy saved by reducing unnecessary production reruns and material discard. Moreover, AI-based inspection systems also enable faster quality checks, reducing idle time and improving line throughput. Overall, the deployment of the CNN-based defect detection module has shown to be both economically and environmentally beneficial in industrial sustainability workflows.

# 5.1. Energy Consumption Reduction

The effectiveness of Sustain AI in optimizing industrial energy usage was assessed by comparing energy consumption before and after the use of AI. A summary of the reduction in energy consumption across various industrial sectors is provided in Table 3.

Table 3. Energy consumption reduction in different sectors.   

<html><body><table><tr><td>Industry Sector</td><td>Energy Usage Before AI (kWh)</td><td>Energy Usage After AI (kWh)</td><td>Reduction (%)</td></tr><tr><td> Steel Manufacturing</td><td>1,200,000</td><td>960,000</td><td>20%</td></tr><tr><td>Cement Production</td><td>850,000</td><td>680,000</td><td>20%</td></tr><tr><td>Automotive Industry</td><td>600,000</td><td>480,000</td><td>20%</td></tr><tr><td>Logistics</td><td>400,000</td><td>340,000</td><td>15%</td></tr></table></body></html>

As shown in Table 3 and Figure 2, the AI-driven optimization led to a significant 0r,0e0d0uction in energy consumpt4i8o0n,,00w0 ith an average improvem2e0n%t of $1 8 . 7 5 \%$ across different 0i,0n0d0ustrial sectors.

![](images/ff07fc32a1324fa230d57eb025a8e42ac2a9ad2378309e31dec3c117114cd57b.jpg)  
Figure2.2.EnEenrgeyrgcyoncsounmsputimonprtieodnucrtieodnuinctidoiffnerien tdsifefcetorresn, thisgehcltigohrtsi,nhgitgheliegffehcttiinvegntehsseoefffAeIc-tiveness of AIbased eneregrygyopotipmtizmatiizoant.ion.

# 5.2. CaarbrobnonFoFotoportipntriRnetduRcetidounction

WOne of the major goals of Sustain AI is to minimize the carbon foo18tpofr i3n1t of manufacturing industries. Table 4 presents the $\mathrm { C O } _ { 2 }$ emission levels before and after the deployment of AI-based optimization techniques.

Table 4. $\mathrm { C O } _ { 2 }$ emission reduction in different sectors.   

<html><body><table><tr><td>Industry Sector</td><td>CO2 Emissions Before AI (tons)</td><td>CO2 Emissions After AI (tons)</td><td>Reduction (%)</td></tr><tr><td> Steel Manufacturing</td><td>320,000</td><td>256,000</td><td>20%</td></tr><tr><td>Cement Production</td><td>220,000</td><td>176,000</td><td>20%</td></tr><tr><td>Automotive Industry</td><td>180,000</td><td>144,000</td><td>20%</td></tr><tr><td>Logistics</td><td>140,000</td><td>119,000</td><td>15%</td></tr></table></body></html>

From Table 4 and Figure 3, it is evident that AI-powered process optimization significantly reduced carbon emissions, with an average reduction of $1 8 . 7 5 \% ,$ aligning with global sgulosbtalinsuasbtialintaybigliotaylgs.o

![](images/726e5e28d3a2b55f91b644d237ea844f6ea33403aa0614221893ede4846ee9fb.jpg)  
Figure 3. . $\mathrm { C O } _ { 2 }$ meismsiiosnsiroendurcetidouncitniodinffienrednitfsfecrteonrst asfetecrtoArIsoapftiemr zAatI onp.timization.

# 5.3. Operational Cost Savings

AI-driven optimization resulted in substantial financial savings by reducing energy costs and improving process efficiency. The estimated cost savings for different industries are shown in Table 5. Figure 4 shows the Operational cost savings across different industries after AI-based optimization.

Table 5. Operational cost savings due to AI optimization.   

<html><body><table><tr><td>Industry Sector</td><td>Operational Cost Before AI (USD)</td><td>Operational Cost After AI (USD)</td><td>Savings (%)</td></tr><tr><td> Steel Manufacturing</td><td>5,000,000</td><td>4,100,000</td><td>18%</td></tr><tr><td>Cement Production</td><td>3,500,000</td><td>2,870,000</td><td>18%</td></tr><tr><td>Automotive Industry</td><td>2,800,000</td><td>2,300,000</td><td>17.8%</td></tr><tr><td>Logistics</td><td>1,900,000</td><td>1,615,000</td><td>15%</td></tr></table></body></html>

![](images/4679737b26b8caae2e2d95bb9f0e39a84511395c4e3144c71cdf17f4fc504a95.jpg)  
Figure 4. Operational cost savings across different industries after AI-based optimization.

The reduction in operational costs across multiple industries resulted in an overall $1 7 . 2 \%$ savings, demonstrating the cost-effectiveness of the proposed model.

# 5.4. Model Performance Evaluation

To assess the accuracy of the predictive models used in Sustain AI, we evaluate thTeo aMsseeasns tAhbesaoclcutueraEcryrorf t(hMeAprEe)daicntdiveRomotodMelesaunseSdquinarSeuEstrarionr (ARI,MwSeE)evfaolruaetnetrhgey cMoenasnumApbtsiolnu tfeorEercraosrti(nMg.

As seen in Table 6 and Figure 5, the Transformer-based model outperforms other approaches, achieving the lowest MAE and RMSE values, indicating higher accuracy in predicting energy consumption trends.

Table 6. Model performance evaluation for energy consumption forecasting.   

<html><body><table><tr><td>Model</td><td>MAE</td><td>RMSE</td></tr><tr><td>LSTM (Long Short-Term Memory)</td><td>8.2</td><td>12.5</td></tr><tr><td>CNN-LSTMHybrid</td><td>6.8</td><td>9.8</td></tr><tr><td>Transformer-BasedModel</td><td>5.3</td><td>8.2</td></tr></table></body></html>

![](images/4472522c053a4ca07d1608d6f22535f00b2aed2201a1b9f67da9bd5beb767549.jpg)  
Figure 5. Comparison of AI model performance for energy forecasting. The Transformer-based model outperforms others with the lowest error values.

# 5.5. System Response Time and Scalability

In order to measure the efficiency of AI-based decision-making, we consider system response time in different industrial environments. Table 7 shows the Average AI decision response time.

Table 7. Average AI decision response time.   

<html><body><table><tr><td>Industry Sector</td><td>Average Response Time (ms)</td></tr><tr><td>Steel Manufacturing</td><td>120</td></tr><tr><td>Cement Production</td><td>110</td></tr><tr><td>Automotive Industry</td><td>105</td></tr><tr><td>Logistics</td><td>98</td></tr></table></body></html>

Sustain AI demonstrates fast response times, ensuring real-time adaptability for industrial processes.

# 5.6. Discussion

The results show that the energy consumption is successfully optimized, the carbon emissions are reduced, and industrial efficiency is improved by using the Sustain AI model. The key observations of the study are the following:

Energy Efficiency: Industries saved 18.75 percent on average in energy consumption, amounting to huge cost savings.   
. Environmental Impact: AI-powered optimization implementation lowered $\mathrm { C O } _ { 2 }$ emissions by $1 8 . 7 5 \%$ , on average, thus contributing to sustainability. Financial Viability: The operational cost savings resulted in $1 7 . 2 \%$ saved operations, which made it economically reasonable.   
. Model Accuracy: The Transformer-based deep learning model offered the best forecasting accuracy and, thus, reliable energy consumption predictions.   
. Real-Time Adaptability: Sustain AI’s decision-making system had a $1 0 8 \mathrm { { m s } }$ average response time, which is practical for industrial use.

The experimental results validate the findings of Sustain AI as a robust and scalable solution to industrial energy optimization. The integration of AI-driven predictive analytics, reinforcement learning, and real-time decision-making, along with the integration of the proposed model, would improve operational efficiency and have the potential to achieve global sustainability initiatives.

# 5.7. Sustain AI: Multi-Modal Deep Learning for Reducing Carbon Footprint in Manufacturing

This subsection assesses the performance of the proposed Sustain AI framework, which leverages multilingual tandem deep learning for the optimization of energy efficiency, a reduction in carbon footprint, and an improvement in industrial manufacturing in terms of sustainability. The results show the effect of AI-based strategies on operational metrics, such as energy consumption, $\mathrm { C O } _ { 2 }$ emissions, waste heat recovery, etc., as well as supply chain optimization, defect detection, and HVAC energy efficiency.

# 5.7.1. Energy Consumption Optimization in Smart Factories

Industrial energy consumption was analyzed before and after the AI implementation to assessttheeefefffecetctofofAIA-bI-absaesdeednerngeyrgoyptoipmtizmatiizoanti.oTna.blTea8blseu8msmuamrizmeasrtizhesi tmhperoivmepmroevntes-. Figure 6 shows Energy consumption trends before and after AI-based optimization in smart ifnacstomraierts foavcteorr1ie0s0 toivmere i1n0t0etrivmales.i

Table  8.. Energy consumption  optimization in smart factoriies.   

<html><body><table><tr><td>Time</td><td>Before AI (kWh)</td><td>After AI (kWh)</td></tr><tr><td>0</td><td>95</td><td>82</td></tr><tr><td>20</td><td>110</td><td>84</td></tr><tr><td>50</td><td>108</td><td>85</td></tr><tr><td>80</td><td>113</td><td>87</td></tr><tr><td>100</td><td>110</td><td>93</td></tr></table></body></html>

Energy Consumption Optimization in Smart Factories 120 BeforeAlImplementation AfterAl Implementation 70 0 20 40 60 80 100 Time (Arbitrary Units)

# 5.7.2. $\mathrm { C O } _ { 2 }$ Emission Reduction Through AI-Based Smart Scheduling

With intelligent smart scheduling and energy management brought by AI, the $\mathrm { C O } _ { 2 }$ emissiWoinths firnotemll ivgaernitosusmianrtdsucsthreidalusliencgtoarsndwenre rgeydumcaendasgiegnmiefinctabnrtloyu. gThte $\mathrm { C O } _ { 2 }$ ,etmhiessCioOn2 reemdiusscitions ifsroprmesveanrtieodusininTdaubslteri9a. Fsiegcutroers7 swheorewrsetdhuecRededsiugctniiofincain $\mathrm { C O } _ { 2 }$ ehemiCssOi2onesmaiscsriosns irnedusctirioanl iospepraetsieontsedovienr Tseaqblue n9t.iaFligtiumre  7pesrhiodwsutshiengReAdI-ubctaisoendisnchCeOd2uelimnigs.s

Table 9. $\mathrm { C O } _ { 2 }$ emission reduction due to AI-based smart scheduling.   

<html><body><table><tr><td>Time</td><td>Before AI (Metric Tons)</td><td>After AI (Metric Tons)</td></tr><tr><td>0</td><td>360</td><td>320</td></tr><tr><td>20</td><td>375</td><td>310</td></tr><tr><td>50</td><td>380</td><td>300</td></tr><tr><td>80</td><td>390</td><td>315</td></tr><tr><td>100</td><td>370</td><td>305</td></tr></table></body></html>

![](images/832082d000333483baf6609556dd4029a41f58928187d51854ebf2afdab9f992.jpg)  
tFoigAurIreo7p. tRiRemediuzucacttiioni.in $\mathrm { C O } _ { 2 }$ 2 emiissiionssaaccrroosssininddusutsrtirailalopoepreartaitoinosnsovoevresresqeuqeunetinatli atilmtiempe rpieordiosdus iunsgAI-based scheduling.

# 5T.a7b.l3e. 1W0.asWteastHeehaetatRreeccovery eEfffifciciieenccyybIefmorperaonvde amfteenrtAUIsoipntgimAiIz

sAtIr-ydriven waste heat recEoffivecrieynscys tBeemfsorseigAnIif(ic%a)ntly imEpffirocivendcyenAefrtgeyr eAfIfi(ci%e)ncy by oCpetmimeniztinMganhuefatcrteuurisnegproces6s5es in industrial plants. The 7ef8ficiency improvements are sStheoewl nPriondTuacbtlieon10. Figure 8 sh7o2ws the Waste heat recovery ef8fi5ciency improvements due to PAaIpoeprtiInmdizuastiroyn.

![](images/00bf3b1334961d42812f83d1ab13b6388daccbff7230bed89815c537721a0a50.jpg)  
Figure 8. Waste heat recovery efficiency improvements due to AI optimization.

Table 10. Waste heat recovery efficiency before and after AI optimization.   

<html><body><table><tr><td>Industry</td><td>Efficiency Before AI (%)</td><td>Efficiency After AI (%)</td></tr><tr><td>Cement Manufacturing</td><td>65</td><td>78</td></tr><tr><td>Steel Production</td><td>72</td><td>85</td></tr><tr><td>Paper Industry</td><td>60</td><td>75</td></tr></table></body></html>

# 5.7.4. AI-Powered Carbon Capture Efficiency Improvement

Carbon capture prroccessseessininininddusutsrtirailapl lpalnatnstswewre roeptoipmtizmeidzeudsiunsgi nAgI-dArIi-vderinvdeencidsieocin-- smioank-inmga kminogdelms,osdieglns,ifsiciagntilfiycianctrleyaisnincrgecaasirnbgoncsaerqbouenstsreaqtiuoensterfaftiicoien ceyffi. cTiaebnlcey1.1Toaubtllein1e1s tohuetlsieniems tphroesve imenptrso.vFeigmuernets9.sFhioguwrseth9eshAoI-wdsritvhenAcaI-rdbroinv ecnapctaurrbeoenffcicaipetnucryeiemffipcrioevnecmyeinmt-s pacrrovsesminedntus tarcireos.s

Table 11. Carbon capture efficiency before and after AI optimization.   

<html><body><table><tr><td>Industry</td><td>Carbon Capture Efficiency Before AI(%)</td><td>Efficiency After AI (%)</td></tr><tr><td>Chemical Industry</td><td>58</td><td>72</td></tr><tr><td>Oil and Gas Plants</td><td>60</td><td>75</td></tr><tr><td>Power Plants</td><td>55</td><td>70</td></tr></table></body></html>

# Al-Powered Carbon Capture Efficiency in Industrial Plants

BeforeAl-BasedCarbonCapture   
80 × AfterAl-Based Carbon Capture X × X X X   
55 ×   
50 0 10 20 30 40 50 Time (Arbitrary Units)

# 5.7.5. AI-Driven Smart HVAC Systems in Manufacturing Plants

The use of AI-powered HVAC systems significantly reduced energy wastage while maintaining optimal temperature conditions. Table 12 presents the efficiency improvements. Figure 10 shows the Energy usage comparison before and after AI implementation in smart HVAC systems across automotive, electronics, and food processing plants.

Table 12. Energy savings in smart HVAC systems.   

<html><body><table><tr><td>Industry</td><td>Energy Usage Before AI (kWh)</td><td>Energy Usage After AI (kWh)</td></tr><tr><td>Automotive Plants</td><td>150</td><td>120</td></tr><tr><td>Mentroniuring</td><td>140</td><td>112</td></tr><tr><td>Food Processing</td><td>130</td><td>105</td></tr></table></body></html>

![](images/247883c656a7a6cfa6d78cd4dc11effe1d961d75a9c9b6af6d376652828dc80b.jpg)  
Foidg uPreo1c0e.ssEinegrgy usage comp1a3ri0son before and after AI implementa1t0io5n in smart HVAC systems across automotive, electronics, and food processing plants.

# 75..67.6.AIA-ID- rDirviveen Process Opttiimiizzataitoinonini nSteSteleeMl aMnuafnauctfuarcitnugr

iFgiugruere111demonstratesttheiimppacatctofofAI-AbIa-sbeadsefudrfnuarcneatecemtpe rmaptuereatouprtiemoipztaitimoinziant esetelelmananufuafactcturriing, leadingttoeennhahnacnecdeednerngeyrgefyfieciffienccieynacnydaenmdisesimoinsrseiodnucrtieodnus.c

![](images/1c01e3f03f2643d02407389d86f0bf9976efc81167445c300f95bbc277e86dfa.jpg)  
Figure 11. Effect of AI-driven furnace temperature control on energy efficiency and emissions in steel gmuarneuf1a1c.tuErffinegc.t

# 5.7.7. AI-Optimized Supply Chain for Carbon Reduction

AII--OdrpitviemniszuepdplSyucphpaliyn oCphtiaimnizfaotironCsarhbaovenleRde tdouscitginoinficant reductions in transportation emissions, as shown in Figure 12.

Table 13 shows the Impact of AI-optimized supply chain on carbon reduction.

![](images/80c0e5db01bd2f660227f0d4a4a8c0df23447fd583e6f788104b5ef5945ca6ec.jpg)  
Figure 12. Transportation emissions, fuel consumption, and delivery performance improvements via AI-driven supply chain optimization across logistics and manufacturing sectors.

Table 13. Impact of AI-optimized supply chain on carbon reduction.   

<html><body><table><tr><td>Parameter</td><td>Before AI</td><td>After AI</td><td>Reduction (%)</td></tr><tr><td>Transportation Emissions (Metric Tons)</td><td>300,000</td><td>270,000</td><td>10%</td></tr><tr><td>Fuel Consumption (Liters)</td><td>1,200,000</td><td>1,020,000</td><td>15%</td></tr><tr><td>Delivery Time (Hours)</td><td>36</td><td>30</td><td>16.7%</td></tr><tr><td>Idle Time in Warehouses (Hours)</td><td>10</td><td>7</td><td>30%</td></tr></table></body></html>

# 5.7.8. Indus5t.r7i.a8l.IIonTdaunsdtriAaIlfIorTS amnardt EAnIefrogrySMmoanritoErinegr

AI-enabledAIoI-Tesnyastbelemds IwoeTresyismtpelmems ewnetered itommploenmiteorntreadl-ttoi meoenietrogryruesaal-gtei,mredeunceing wastagien,gaswdaesptiactged, ians Fdiegpuircet1e3d. iTnabFlieg1u4resh1o3.wsTtahbeleIm14paschto fwisntdhuestIrimalpIaocTt aonfdinAdI on smart enoenrgsymarotneitnoerirnggy.

![](images/b763adb0565e09f8b8e8b807eaa45349b9ee48a3d4df6ba13a9f97afa84ea229.jpg)  
Figure 13. Industrial IoT and AI for smart energy monitoring.

ETaVIbEleW14. Impact of industrial IoT and AI on smart energy monitoring.   

<html><body><table><tr><td>Parameter</td><td>Before AI</td><td>After AI</td><td>Improvement (%)</td></tr><tr><td>Energy Wastage (kWh)</td><td>500,000</td><td>375,000</td><td>25%</td></tr><tr><td>Real-time Monitoring Efficiency (%)</td><td>70</td><td>92</td><td>22%</td></tr><tr><td>Downtime Due to Power Issues (Hours)</td><td>15</td><td>8</td><td>46.7%</td></tr><tr><td>Cost Savings (USD)</td><td>1,200,000</td><td>1,500,000</td><td>25%</td></tr></table></body></html>

Deep learniing modellsiimproved defectt dettecttiion,, reduciing matterriiall wastte,, asiilllus-- trated in Figure 14.

![](images/008ae53707ae58face0d416f71117b1aee4b2e905f51887e29e180fe6596140f.jpg)  
Figure 14. Defect rate reduction and material efficiency improvements using CNN-based defect detection with a dataset of 38,000 samples from steel and automotive lines.

5..7..9.. Defect Detection  and  Material  Efffificiency Improvement   
Table 15 shows the Impact of AI-based defect detection on material efficiency.   
Table 15. Impact of AI-based defect detection on material efficiency.   

<html><body><table><tr><td>Parameter</td><td>Before AI</td><td>After AI</td><td>Improvement (%)</td></tr><tr><td>Defect Rate (%)</td><td>35</td><td>20</td><td>42.8%</td></tr><tr><td>Material Waste (Tons)</td><td>500</td><td>350</td><td>30%</td></tr><tr><td>Inspection Time (Hours)</td><td>12</td><td>6</td><td>50%</td></tr><tr><td>Cost Savings (USD)</td><td>800,000</td><td>1,200,000</td><td>33.3%</td></tr></table></body></html>

The experimental results demonstrate that Sustain AI effectively reduces industrial energy consumption, minimizes $\mathrm { C O } _ { 2 }$ emissions, and optimizes sustainability-related paeranemregtyercs.onTshuemkpetyi otank, emaiwnaiymsiiznecsluCdOe2tehme ifsoslilonwsi,nagn: $- 2 0 \%$ irme idzuecstisounsitaniennaebrilgityyu-rsealgaeteadcrpoas-s industries; $1 5 \mathrm { - } 2 0 \%$ lower carbon emissions due to AI-optimized processes; enhanced carbon capture and waste heat recovery efficiencies; AI-driven HVAC systems improved energy efficiency by up to $1 8 \%$ ; $2 0 \%$ reduction in energy usage across industries; $1 5 \mathrm { - } 2 0 \%$ ilomwprero vcaerdboenermg iysseioffincsiednuceytboyAIu-po pttoim18iz%e;d2p0r%ocreessdeusc;teionnhaincednecragrybounscaagpetaucrreoasnsdinwdausst-e thrieast;r1e5co–2v0er%y leoffwiceirencairebs;oannedmAiIs-sdiorinvsend uHeVtAoCAsI-yostpetimsiizmedprporvoecdesesnesr;geynehfafincicendcycabrybounp ctao $1 8 \%$ e.

These findings confirm that AI-powered industrial optimization is a viable solution for acThiesveinfignsduinstgasincaobnilfiirtymatnhdatefAfIic-ipeoncwye riendmianndufsatrcitaulrionpgt.i

To validate the robustness of the reported percentage improvements, we conducted paired $t { \cdot }$ -tests comparing pre- and post-AI optimization results across key industrial sectors. As shown in Table 16, all performance metrics demonstrated statistically significant improvements with $p$ -values below 0.01. This confirms that the observed gains are unlikely due to random variance and reflect genuine performance enhancement attributable to the Sustain AI framework. These statistical tests strengthen the evidence for the model’s practical superiority in energy efficiency, emission reduction, and operational cost optimization.

Table 16. Statistical significance of improvements using Sustain AI (paired t-test results).   

<html><body><table><tr><td>Evaluation Metric</td><td>p-Value (t-Test)</td><td>Significance Level</td><td>Interpretation</td></tr><tr><td>Energy Consumption Reduction</td><td>0.0032</td><td>p <0.01</td><td> Statistically significant improvement</td></tr><tr><td>CO2 Emission Reduction</td><td>0.0045</td><td>p <0.01</td><td> Statistically significant improvement</td></tr><tr><td>Operational Cost Savings</td><td>0.0061</td><td>p <0.01</td><td>Statistically significant improvement</td></tr><tr><td>Waste Heat Recovery Efficiency</td><td>0.0079</td><td>p <0.01</td><td>Statistically significant improvement</td></tr><tr><td>Model Accuracy (RMSE,MAE)</td><td>0.0024</td><td>p <0.01</td><td>Statistically significant error reduction</td></tr></table></body></html>

While the current mention of blockchain is conceptual, we envision using permissioned blockchain networks (e.g., Hyperledger Fabric) with Practical Byzantine Fault Tolerance (PBFT) consensus to ensure energy and emissions data are securely logged without compromising industrial confidentiality. Each transaction—whether it logs real-time energy usage, carbon offsets, or process adjustments—would be recorded as a tamperproof ledger entry. Smart contracts will be developed to automatically verify compliance against regulatory thresholds (e.g., emissions caps, reporting standards) and could even trigger alerts or incentives. These components collectively ensure auditability, trust, and automation in sustainability governance.

# Benchmark Comparison with Traditional Methods:

To objectively assess the performance of Sustain AI, we conducted a comparative evaluation against several traditional industrial optimization and forecasting techniques, including linear regression, multiple regression, and ARIMA models, as well as singlemode AI architectures like standalone LSTM and CNN-only setups. We also compared the results against a baseline rule-based energy controller commonly used in legacy manufacturing systems. Each model was tested on the same preprocessed dataset used in our framework, with identical training–test splits and evaluation metrics (RMSE and MAE). The goal was to highlight not only the accuracy benefits of multi-modal integration and reinforcement learning but also the limitations of conventional approaches in handling dynamic, heterogeneous industrial data. The results show that Sustain AI significantly outperformed all baseline models in terms of predictive accuracy, energy efficiency improvement, and system responsiveness. Additionally, single-mode models failed to capture cross-modal interactions and lacked adaptability, further supporting the value of Sustain AI’s hybrid, deep learning-driven architecture. Table 17 shows the Benchmark comparison of Sustain AI vs. traditional and single-mode models.

Table 17. Benchmark comparison of Sustain AI vs. traditional and single-mode model   

<html><body><table><tr><td>Model</td><td>MAE</td><td>RMSE</td><td>Energy Reduction (%)</td><td>CO2 Reduction (%)</td><td>Avg. Response Time (ms)</td></tr><tr><td>Linear Regression</td><td>18.7</td><td>25.4</td><td>6.5%</td><td>5.9%</td><td>220</td></tr><tr><td>Multiple Regression</td><td>15.3</td><td>21.1</td><td>8.2%</td><td>7.4%</td><td>215</td></tr><tr><td>ARIMA</td><td>14.1</td><td>20.6</td><td>9.5%</td><td>8.0%</td><td>208</td></tr><tr><td>LSTM Only</td><td>8.2</td><td>12.5</td><td>13.6%</td><td>14.2%</td><td>160</td></tr><tr><td>CNN Only</td><td>10.5</td><td>14.8</td><td>12.1%</td><td>11.8%</td><td>145</td></tr><tr><td>Rule-Based Energy Controller</td><td>19.9</td><td>26.3</td><td>5.2%</td><td>4.7%</td><td>230</td></tr><tr><td>SustainAI(ProposedModel)</td><td>5.3</td><td>8.2</td><td>18.75%</td><td>20%</td><td>108</td></tr></table></body></html>

These benchmark results clearly demonstrate that Sustain AI outperforms both classical methods and simpler deep learning setups in key industrial KPIs. The multi-modal data fusion and reinforcement learning components drive substantial gains in accuracy, responsiveness, and sustainability impact.

# 6. Conclusions

This study introduced Sustain AI, a multi-modal deep learning framework designed to reduce the carbon footprint in industrial manufacturing by integrating advanced AI-driven optimization techniques. The proposed system demonstrated significant improvements across multiple operational areas, leveraging Convolutional Neural Networks (CNNs) for defect detection, Recurrent Neural Networks (RNNs) for predictive energy consumption modeling, and Reinforcement Learning (RL) for adaptive energy optimization. The results indicate that energy consumption was reduced by $1 8 . 7 5 \%$ through AI-driven process scheduling, while $\mathrm { C O } _ { 2 }$ emissions decreased by $2 0 \%$ via intelligent supply chain management and energy-efficient operations. Additionally, waste heat recovery efficiency improved by $2 5 \%$ , and smart HVAC systems achieved an $1 8 \%$ reduction in energy waste. The CNN-based defect detection system enhanced material efficiency, reducing defect rates by $4 2 . 8 \%$ and minimizing material waste. Furthermore, real-time IoT-based energy monitoring improved tracking efficiency to $9 2 \%$ , leading to $2 5 \%$ less energy wastage. Future enhancements to Sustain AI will focus on Deep Reinforcement Learning (DRL) techniques to further refine adaptive decision-making in dynamic industrial environments. Additionally, blockchain technology will be integrated to ensure secure, tamper-proof traceability of energy consumption and carbon emissions, improving transparency in sustainability efforts. To validate its scalability, the framework will be tested across diverse industrial domains, including automobile, pharmaceutical, and food industries, ensuring its adaptability beyond manufacturing. This research underscores the transformative role of AI-driven industrial optimization in enhancing sustainability, energy efficiency, and cost-effectiveness. By leveraging advanced deep learning and reinforcement learning strategies, Sustain AI provides a viable pathway toward carbon-neutral, energy-efficient industrial ecosystems, aligning with global sustainability initiatives and Industry 4.0 standards. To enhance the environmental and economic impact of Sustain AI, future iterations of the framework will incorporate long-term emissions pricing models and carbon tax simulations. This addition will enable industries to simulate financial trade-offs between energy optimization strategies and future carbon taxation scenarios. By integrating modules from environmental economics—such as Social Cost of Carbon (SCC) or dynamic carbon pricing models—the framework can provide more actionable, policy-aligned recommendations. This helps stakeholders assess not only the technical benefits but also the fiscal sustainability of emissions reduction plans under regulatory pressure.

# Future Perspectives:

While Sustain AI demonstrates strong results in energy efficiency and carbon reduction, future extensions of this research will explore three key directions. First, we plan to incorporate advanced Deep Reinforcement Learning (DRL) techniques, such as multi-agent and hierarchical ${ \mathrm { R L } } ,$ to improve adaptive process control in more complex industrial environments. Second, we aim to integrate blockchain-based traceability for secure and tamper-proof logging of energy usage and emissions data, which will enhance regulatory compliance and stakeholder trust. Third, the framework will be expanded to support sector-specific customization across industries like pharmaceuticals, electronics, and food processing by training domain-adaptive models. Additionally, upcoming versions of Sustain AI will feature predictive carbon tax simulation modules using environmental economic models to help industries evaluate the financial implications of long-term decarbonization strategies. These advancements will further solidify Sustain AI as a comprehensive, intelligent system aligned with global ESG goals and sustainability standards such as ISO 50001 and the EU Carbon Border Adjustment Mechanism (CBAM).

Funding: This research received no external funding.

Institutional Review Board Statement: Not applicable.

Informed Consent Statement: Not applicable.

Data Availability Statement: The author used data to support the findings of this study, which are included in this article.

Acknowledgments: The author would like to thank the Qassim University for supporting this work.

Conflicts of Interest: The author declares that there are no conflicts of interest regarding the publication of this research paper. The research was conducted in an unbiased manner, and there are no financial or personal relationships that could have influenced the findings or interpretations presented herein.

# References

1. Li, Y.; Wang, Z.; Liu, S. Enhance carbon emission prediction using bidirectional long short-term memory model based on text-based and data-driven multimodal information fusion. J. Clean. Prod. 2024, 471, 143301. [CrossRef]   
2. Ji, T.; Lin, Y.; Yang, Y. Forestadvisor: A multi-modal forest decision-making system based on carbon emissions. Environ. Model. Softw. 2024, 181, 106190. [CrossRef]   
3. Wang, H.; Wang, Y.; Wang, X.; Yin, W.; Yu, T.; Xue, C. Multimodal machine learning guides low carbon aeration strategies in urban wastewater treatment. Engineering 2024, 36, 51–62. [CrossRef]   
4. Wang, S.; Cheng, N.; Hu, Y. Comprehensive environmental monitoring system for industrial and mining enterprises using multimodal deep learning and clip model. IEEE Access 2025, 13, 19964–19978. [CrossRef]   
5. Mantis, A. Generalizable Electrocatalyst Design Framework Combining Multi-Modal Data and Artificial Intelligence; U.S. Department of Energy Office of Scientific and Technical Information: Oak Ridge, TN, USA, 2024.   
6. Song, B.; Zhou, R.; Ahmed, F. Multi-modal machine learning in engineering design: A review and future directions. J. Comput. Inf. Sci. Eng. 2024, 24, 010801. [CrossRef]   
7. Zhao, C.; Sun, J.; Fang, J.; Li, X.; Zhao, F.; Lei, J. Multi-scale hybrid attention aggregation networks for multi-modal monitoring in laser-induced thermal-crack processing. Mech. Syst. Signal Process. 2025, 223, 111883. [CrossRef]   
8. Wang, X.; Yin, X.; Jiang, D.; Zhao, H.; Wu, Z.; Zhang, O. Multi-modal deep learning enables efficient and accurate annotation of enzymatic active sites. Nat. Commun. 2024, 15, 7348. [CrossRef] [PubMed]   
9. Zhang, Y.; Shen, J.; Li, J.; Yao, X.; Chen, X.; Liu, D. A new lightweight framework based on knowledge distillation for reducing the complexity of multi-modal solar irradiance prediction model. J. Clean. Prod. 2024, 475, 143663. [CrossRef]   
10. Korolev, V.; Mitrofanov, A. The carbon footprint of predicting co2 storage capacity in metal-organic frameworks within neural networks. iScience 2024, 27, 109644. [CrossRef] [PubMed]   
11. Boumaraf, S.; Li, P.; Radi, M.A.; Abdelhafez, F. Optimized flare performance analysis through multi-modal machine learning and temporal standard deviation enhancements. IEEE Access 2025, 13, 34362–34377. [CrossRef]   
12. Liu, S.; Xiang, Y.; Zhou, H. A deep learning-based approach for high-dimensional industrial steam consumption prediction to enhance sustainability management. Sustainability 2024, 16, 9631. [CrossRef]   
13. Rehman, M.U.; Jamshed, M. Advances in multi-modal intelligent sensing. In Multimodal Intelligent Sensing in Modern Applications; Wiley: Hoboken, NJ, USA, 2024.   
14. Almujally, N.; Rafique, A.; Mudawi, N.A. Multi-modal remote perception learning for object sensory data. Front. Artif. Intell. 2024, 18, 1427786. [CrossRef] [PubMed]   
15. Hao, Y.; Zhu, L.; Wang, J.; Shu, X.; Yong, J.; Xie, Z. Ball-end tool wear monitoring and multi-step forecasting with multi-modal information under variable cutting conditions. J. Manuf. Process. 2024, 76, 234–258. [CrossRef]   
16. Miao, S.; Liu, Y.; Liu, Z.; Shen, X.; Liu, C.; Gao, W. A novel attention-based early fusion multi-modal cnn approach to identify soil erosion based on unmanned aerial vehicle. IEEE Access 2024, 12, 95152–95164. [CrossRef]   
17. Zhang, L.; Yang, Q.; Zhu, Z. The application of multi-parameter multi-modal technology integrating biological sensors and artificial intelligence in the rapid detection of food contaminants. Foods 2024, 13, 1936. [CrossRef] [PubMed]   
18. Pentayev, A.; Ahrari, A.; Baubekova, A. Towards multi-modal oil spill detection and coverage in the caspian sea: A comprehensive approach. Int. J. Water Resour. Dev. 2025, 41, 176–203. [CrossRef]   
19. Zhou, X.; Yang, Q.; Zheng, X.; Liang, W. Personalized federated learning with model-contrastive learning for multi-modal user modeling in human-centric metaverse. IEEE J. Sel. Areas Commun. 2024, 42, 817–831. [CrossRef]   
20. Cabral, S.; Klimenka, M.; Bademosi, F.; Lau, D.; Pender, S. A contactless multi-modal sensing approach for material assessment and recovery in building deconstruction. Sustainability 2025, 17, 585. [CrossRef]   
21. Bello, R.; Gavrielides, A.; Luong, N. Designing a dynamic platform for the next generation of multi-modal logistics. In Proceedings of the Joint European Conference on Transportation, Antwerp, Belgium, 3–6 June 2024.   
22. Kabashkin, I. Model of sustainable household mobility in multi-modal transportation networks. Sustainability 2024, 16, 7802. [CrossRef]   
23. Altynova, A.; Kozhevin, A.; Dubovik, A. Advancing geoscience with multi-modal ai: A comprehensive copilot. In Proceedings of the Abu Dhabi International Petroleum Exhibition & Conference, Abu Dhabi, United Arab Emirates, 4–7 November 2024.   
24. Shukla, P.R.; Skea, J.; Slade, R.; Al Khourdajie, A.; van Diemen, R.; McCollum, D.; Pathak, M.; Some, S.; Vyas, P.; Fradera, R.; et al. 2022: Mitigation of Climate Change. Contribution of Working Group III to the Sixth Assessment Report of the Intergovernmental Panel on Climate Change; Cambridge University Press: Cambridge, UK; New York, NY, USA, 2022. [CrossRef]   
25. Wróblewska, M.; Zmitrowicz, M. Carbon Border Adjustment Mechanism as proposed EU regulation to combat climate change. In Interaction of Law and Economics: Sustainable Development; ILESD: Brno, Czech Republic, 2024; pp. 260–269. [CrossRef]   
26. Azizi, S.; Radfar, R.; Ghatari, A.R.; Nikoomaram, H. Assessing the impact of energy efficiency and renewable energy on $\mathrm { C O } _ { 2 }$ emissions reduction: Empirical evidence from the power industry. Int. J. Environ. Sci. Technol. 2025, 22, 2269–2288. [CrossRef]

Disclaimer/Publisher’s Note: The statements, opinions and data contained in all publications are solely those of the individual author(s) and contributor(s) and not of MDPI and/or the editor(s). MDPI and/or the editor(s) disclaim responsibility for any injury to people or property resulting from any ideas, methods, instructions or products referred to in the content.