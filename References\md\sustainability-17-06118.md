Article

# Leveraging Satellite Imagery and Machine Learning for Urban Green Space Assessment: A Case Study from Riyadh City

Meshal Alfarhood ${ \ast \textcircled { \circ } }$ , <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>

Academic Editor: <PERSON> <PERSON>

Received: 25 May 2025   
Revised: 1 July 2025   
Accepted: 2 July 2025   
Published: 3 July 2025

Citation: <PERSON>, M.; <PERSON><PERSON>, A.; Alalwan, A.; Al<PERSON>b, F. Leveraging Satellite Imagery and Machine Learning for Urban Green Space Assessment: A Case Study from Riyadh City. Sustainability 2025, 17, 6118. https://doi.org/10.3390/ su17136118

Copyright: $\odot 2 0 2 5$ by the authors. Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (https://creativecommons.org/ licenses/by/4.0/).

Department of Computer Science, College of Computer and Information Sciences, King Saud University,   
P.O. Box 51178, Riyadh 11543, Saudi Arabia   
\* Correspondence: <EMAIL>

# Abstract

The “Green Riyadh” project in Saudi Arabia represents a major initiative to enhance urban sustainability by expanding green spaces throughout Riyadh City. The initiative aims to improve air and water quality, increase tree and plant coverage, and promote environmental well-being for city residents. However, accurately assessing the extent and quality of green spaces remains a significant challenge. Current methods for evaluating green areas and measuring tree density are limited in precision and reliability, preventing effective monitoring and planning. This paper proposes an innovative solution that leverages live satellite imagery and advanced deep learning techniques to address these challenges. We collect extensive satellite data from two sources and then build two separate analytical pipelines. These pipelines process high-resolution satellite imagery to identify trees and measure green density in vegetated areas. The experimental results show significant improvements in accuracy and efficiency, with the YOLOv11 model achieving a $\mathrm { m A P @ 5 0 }$ of $9 5 . 4 \%$ , precision of $9 4 . 6 \%$ , and recall of $9 0 . 2 \%$ . These findings offer a scalable and reliable alternative to traditional methods, enabling comprehensive progress evaluation and facilitating informed decision-making for urban planning. The proposed methodology not only supports the objectives of the “Green Riyadh” project but also sets a benchmark for green space evaluation that can be adopted by cities worldwide.

Keywords: satellite imagery; greenery assessment; remote sensing; machine learning; YOLO; NDVI

# 1. Introduction

Urban green spaces play a vital role in enhancing the quality of life in cities by improving air quality, reducing urban heat, and providing recreational areas for residents. They contribute significantly to environmental sustainability and urban resilience, particularly in dry regions where green spaces are scarce [1]. Research shows that the expansion of urban greenery can mitigate climate impacts and improve public well-being, making it a major key to sustainable urban development initiatives.

The “Green Riyadh” initiative [2] is a transformative project, launched in 2019, aimed to enhance the sustainability of Riyadh City, the capital city of Saudi Arabia. As part of the Kingdom’s Vision 2030, this initiative focuses on expanding green spaces, improving air and water quality, and fostering a more ecologically balanced urban environment for residents. Specifically, it aims to increase the city’s green cover by planting 7.5 million trees in diverse locations, including 3330 neighborhood gardens, parks, streets, schools, mosques, and healthcare facilities [3].

However, accurately assessing and monitoring green areas remains a critical challenge. Current and traditional techniques for assessing green spaces and quantifying tree density often lack the accuracy and consistency needed for efficient monitoring and strategic planning [4,5]. To address these challenges, this paper aims to fill this gap by utilizing state-of-the-art techniques, including live satellite imagery retrieval and advanced image detection techniques, to assist decision-makers in assessing the condition of green spaces. Satellite imagery enables fast and large-scale analysis of green zones compared to traditional methods, while advanced image detection algorithms identify specific patterns in tree and plant distributions, allowing precise quantification of tree numbers and green area density. This novel approach generates reliable data for evaluating the progress of the “Green Riyadh” project and supports data-driven decision-making processes for future planning [6].

In particular, we develop a dual pipeline methodology integrating Sentinel-2 NDVI analysis for green area coverage and deep learning techniques for precise tree counting. This novel methodology provides a macro (percentage of green areas) and micro (individual tree counts) view of urban greenery, enabling a comprehensive assessment of Riyadh’s greenery and green initiatives like the “Green Riyadh initiative”. Specifically, our work gathers a unique dataset using high-resolution imagery from Google Earth Maxar Technologies and Sentinel-2 satellites. Through augmentation, we expanded 732 high-resolution images to 6647, enhancing model robustness. This diverse dataset supports precise tree detection and broad-scale green area analysis across Riyadh’s urban landscape. Moreover, we investigate several state-of-the-art object detection models, including YOLOv7 [7], YOLOv8 [8], YOLOv11 [9], and RT-DERT [10]. Our chosen model for the methodology, YOLOv11, demonstrates exceptional performance metrics compared to the others, achieving a mean Average Precision $( \mathbf { m A P @ 5 0 } )$ of $9 5 . 4 \%$ , precision of $9 4 . 6 \%$ , and recall of $9 0 . 2 \%$ . These results underscore its superior accuracy and efficiency in object detection tasks. By leveraging high-resolution satellite imagery, we fill the gap in the literature, offering a robust solution for detailed urban tree monitoring to support sustainable planning.

The implementation of our methodology not only enhances the “Green Riyadh” initiative’s ability to achieve its sustainability goals but also establishes a model for smart city development worldwide. Leveraging a similar methodology in urban centers globally can significantly contribute to achieving sustainable development goals (SDGs) by improving urban greenery and promoting environmental resilience. This paper explores the integration of satellite imagery and advanced image recognition in the “Green Riyadh” initiative. It emphasizes how this technological synergy addresses the critical challenges of measuring green coverage and greenery areas, ensuring more informed decision-making for a sustainable urban future. This paper contributes to the field of urban sustainability by introducing a replicable model for green space assessment, ultimately promoting a sustainable future for urban areas globally.

The main contributions of this paper can be summarized in three key aspects:

First, we collected a comprehensive dataset for Riyadh City using imagery from two different satellites, specifically aimed at evaluating green areas and estimating tree counts. This dataset provides a valuable resource for urban environmental studies. Second, we developed a unique dual pipeline that operates in parallel and explored multiple state-of-the-art models, resulting in highly accurate results, with a precision rate of $9 4 . 6 \%$ . This innovative approach enhances the reliability of our analyses. • Third, our findings have been applied in a real-world scenario and are displayed on a user-friendly dashboard, facilitating accessibility for stakeholders and decision-makers interested in urban planning and environmental management.

The remainder of this paper is organized as follows: Section 2 reviews the related work, highlighting key studies and methodologies that inform our research. In Section 3, we present our methodology, detailing the processes of data collection and data preprocessing that support our analysis. Section 4 discusses the results of our study, addressing both the findings and their limitations. Finally, Section 5 provides a conclusion, summarizing the key insights and suggesting directions for future research.

# 2. Related Work

Satellite imagery has proven to be an invaluable tool in a wide range of applications such as urban planning, environmental monitoring, disaster management, and humanitarian aid. Studies in this domain focus on enhancing image quality, improving detection accuracy, and addressing challenges such as shadows and classification complexities. In this section, we provide a comprehensive overview of the existing research relevant to this study based on leveraging satellite imagery and advanced image recognition and classification techniques.

# 2.1. Satellite Imagery Across Different Domains

Satellite imagery has revolutionized research across numerous disciplines by providing a unique, close, and temporal view of the Earth’s surface. Dare [11] addresses the challenge of shadows in high-resolution satellite imagery, specifically in urban areas. The research explores methods like thresholding and radiometric enhancement for shadow detection, demonstrating how effective shadow removal can enhance the clarity of urban imagery. The study, conducted using Melbourne, Australia, as a case study, highlights the varying efficacy of removal methods based on imaging conditions. In addition, Asokan et al. [12] focused on satellite image processing techniques for classifying historical maps, emphasizing the dynamic nature of land boundaries. The study reviews methods like feature extraction, segmentation, and classification, stressing the importance of selecting the right techniques for accurate historical map analysis. Another work by Verbesselt et al. [13] proposed a time-series-based approach for near real-time disturbance detection using satellite imagery. The study, applied to both simulated and real-world data (MODIS NDVI composites of Somalia), demonstrated the method’s robustness in identifying disturbances such as droughts without relying on specific thresholds or time series gap filling. This technique showcases the potential for global-scale monitoring of ecological disturbances.

Moreover, Gadamsetty et al. [14] introduced a deep learning approach combining YOLOv3 and SHA-256 hashing to detect ships in satellite imagery. Their method, tested on a Kaggle dataset, ensured secure and accurate ship detection, improving performance under adverse weather conditions and securing sensitive maritime surveillance data. On the other hand, Yarlagadda et al. [15] used Generative Adversarial Networks (GANs) and a one-class Support Vector Machine (SVM) to detect and localize forgeries in satellite images. The algorithm effectively identified forged areas, offering a significant advancement in satellite image forensics by ensuring the authenticity of the images.

Recently, Siok et al. [16] proposed a new methodology for creating higher-quality remote sensing images by simulating a special high-resolution band from combined aerial and satellite data, which resulted in pan-sharpened images with significantly less spectral distortion than traditional methods. On the other hand, Bai et al. [17] proposed a deep learning framework called MANET, which improved weather and cloud classification by fusing satellite image data with meteorological information like the season, date, and geographic location.

# 2.2. Image Recognition and Object Detection Across Different Domains

Image recognition and object detection are powerful computer vision technologies that have broad applications across many areas of the literature. For example, Iglovikov et al. [18] achieved third place in the DSTL Satellite Imagery Feature Detection Kaggle competition by adapting a U-Net architecture for multispectral data. Their work significantly improved semantic segmentation quality by integrating data fusion strategies and training for specific classes, making their approach scalable for automated satellite feature labeling. Also, Al-Ghrairi et al. [19] proposed a classification approach using color moments features and K-means clustering to categorize land cover in Baghdad, Iraq. Their method achieved an impressive $9 2 . 1 2 \%$ classification accuracy, improving land cover classification for urban environments by focusing on moment features like the mean, standard deviation, and skewness. Moreover, Sladojevic et al. [20] developed a deep convolutional neural network (CNN) model for plant disease recognition using leaf images. With a dataset of 33,469 images, the model demonstrated an impressive classification accuracy of $9 6 . 3 \%$ , offering valuable insights for automated agricultural disease diagnosis.

Moreover, Sibiya and Sumbwanyambe [21] utilized CNNs to identify maize leaf diseases, achieving high accuracy in distinguishing between different diseases. Their approach demonstrated the potential of deep learning in agricultural disease recognition, offering a robust solution for plant health monitoring. Also, He et al. [22] explored the use of deep learning for forest tree species classification using Sentinel-2 satellite imagery on the Google Earth Engine. Their CNN-based method achieved over $9 0 \%$ accuracy, providing an effective and scalable approach for biodiversity monitoring. On the other hand, Sun et al. [23] and Huixian [24] also investigated the use of deep learning for plant species identification. Sun’s team achieved a $9 1 . 7 8 \%$ recognition rate with a 26-layer deep learning model applied to a large dataset of 10,000 plant images, while Huixian’s research highlighted the superior performance of deep learning over traditional methods for plant recognition.

Furthermore, Bodhwani et al. [25] employed deep residual networks to classify tree species from the LeafSnap dataset, achieving a $9 3 . 0 9 \%$ recognition rate. Their work demonstrates the power of deep residual networks in botanical species classification, significantly advancing identification accuracy. Additionally, Kanda et al. [26] focused on using fine-tuned residual neural networks for recognizing tomato leaf diseases. Their study achieved a remarkable $9 9 . 5 \%$ F1-score, showcasing the efficiency and robustness of their deep learning model for plant disease identification.

Recently, transformer-based models have gained significant attention in object detection tasks. These models, particularly those based on the Real-Time Detection Transformer (RT-DETR) architecture, have demonstrated strong performance in identifying and localizing objects with high precision and efficiency. For instance, RT-DETR has been utilized in various detection tasks, such as tomato fruit detection [27,28], emergency vehicle detection [29], and forest smoke detection [30].

# 3. Methodology

Our methodology for evaluating green areas and measuring tree density consists of two main pipelines, as shown in Figure 1, where both pipelines complement each other. The first pipeline focuses on the extent of greenery (i.e., green area percentage and coverage) using the NDVI technique, while the second pipeline quantifies the number of trees in each district of Riyadh City using YOLOv11. The integrated results are then visualized in a unified dashboard, enabling stakeholders to comprehensively assess Riyadh’s urban greenery. This novel methodology supports sustainable urban planning initiatives like the “Green Riyadh” project by providing actionable insights for enhancing green spaces. The following sections illustrate each part extensively.

![](images/0fcad3475bc59c43974d7f96bfff224d45d5ccf48b90ef702e8642ef68e9aa31.jpg)  
Figure 1. Illustration of our overall employed dual pipeline, showcasing the two main components of the methodology.

# 3.1. Data Collection

After reviewing potential data sources, we have chosen to utilize Google Earth and Sentinel-2 for our satellite imagery needs. This decision was based on their ability to provide the highest resolution imagery available with free access, which is crucial for detailed vegetation analysis in urban settings like the city of Riyadh. Additionally, Google Earth offers the capability to access historical satellite images across different time periods. This feature is particularly valuable as it enables us to track changes over time and assess the effectiveness of the "Green Riyadh" initiative in enhancing urban green spaces. Table 1 presents a comparison of different satellite data sources based on three criteria: image resolution, accessibility, and update frequency.

Table 1. A comparison of various satellite data sources evaluated according to three criteria: image resolution, accessibility, and update frequency.   

<html><body><table><tr><td>Data Source</td><td>Image Resolution</td><td>Accessibility</td><td>Update Frequency</td></tr><tr><td>Landsat 8 (NASA)</td><td>15 m (panchromatic), 30 m (multispectral),100 m (thermal)</td><td>Free, open access</td><td>Every 16 days</td></tr><tr><td>MODIS (NASA)</td><td>250 m to 1 km (varies by band)</td><td>Free, open access</td><td>Daily</td></tr><tr><td>Ikonos</td><td>0.82 m (panchromatic),3.2 m (multispectral)</td><td>Restricted,commercial access</td><td>On demand</td></tr><tr><td>QuickBird</td><td>0.65 m (panchromatic),2.62 m (multispectral)</td><td>Restricted, commercial access</td><td>On demand</td></tr><tr><td>Google Earth</td><td>Varies win .y mp to 15.cm)</td><td>Free (for basic use), commercial options for higher</td><td>Continuously updated, but varienthsapon ximately in Riyadh)</td></tr><tr><td>WorldView-3</td><td>0.31 m panchromatic, 1.24 m multispectral</td><td>Restricted, commercial access</td><td>1 day (depends on the target)</td></tr><tr><td>Sentinel-2</td><td>10 m (oanchromatic), 20 m (multispectral), 60 m (SWIR)</td><td>Free, open access</td><td>Every 5 days (with two satellites in orbit)</td></tr></table></body></html>

In particular, we have collected two datasets that complement each other from two different satellite imagery sources, as follows:

Sentinel-2: (eos.com) Sentinel-2 provides multispectral imagery across 13 bands with a spatial resolution of $1 0 { - } 6 0 ~ \mathrm { m }$ . Sentinel-2 is widely used for vegetation monitoring and NDVI computation due to its free accessibility and high-quality data [31]. With a resolution of $1 0 { - } 2 0 ~ \mathrm { m } .$ , Sentinel-2 provides us with data to calculate the Normalized Difference Vegetation Index (NDVI) [32], which measures vegetation density and health. By calculating NDVI, we can estimate how much of each district is covered in green spaces, including trees, grass, and other vegetation.

Google Earth Maxar Technologies: (maxar.com) The dataset for our tree detection pipeline consisted of high-resolution, orthorectified imagery acquired from the Google Earth Pro platform. These visual data originate from Maxar Technologies’ WorldView3 and WorldView-4 sensing platforms [33]. The imagery utilized is a vendor-processed product. The data accessible via Google Earth has undergone significant preprocessing, including radiometric and geometric corrections, and is delivered as a pan-sharpened composite. This data fusion technique merges the high spatial resolution of the satellite’s panchromatic sensor, which provides a resolution of $0 . 3 1 \mathrm { m } ,$ with the spectral information from its lower-resolution multispectral bands (with a resolution of $1 . 2 4 \mathrm { m } \mathrm { , }$ . The result is a single raster dataset with high spatial detail and natural color representation. We downloaded 61 images captured in August 2022 covering the urban extent of Riyadh. Each image covers an area of $3 6 7 \mathrm { m } \times 1 4 1 . 5 \mathrm { m } ,$ with an altitude of $3 1 8 \mathrm { m }$ . We then tiled each one of them into 12 smaller images, resulting in a total of 732 images. These very high-resolution images $( 3 0 \mathrm { c m } \times 3 0 \mathrm { c m } )$ ) per pixel are suitable for tree detection, allowing us to pinpoint individual trees, even in densely built urban areas. This level of detail is crucial for accurately counting trees in Riyadh, as it helps us detect small clusters of trees or isolated green spaces that might otherwise be missed. Initial labeling identified 6200 trees across the images. Through data augmentation techniques, we expanded the dataset to 6647 images, enhancing the reliability and comprehensiveness of our urban tree mapping analysis. Moreover, we took into consideration gathering images from various districts across Riyadh. This diversity in geographic data allowed the model to adapt to varying tree densities, levels of urbanization, and types of vegetation found throughout the city, improving its ability to generalize across different areas. A sample of this dataset is shown in Figure 2.

![](images/03a1a5dc10a8a675f0781380d7675f86ea12079b18ee27e75b4545dbefc89d67.jpg)  
Figure 2. Sample images from the collected Google Earth Maxar dataset.

# 3.2. Data Preprocessing

After collecting our two datasets designated for different pipelines, several preprocessing steps were carried out on both Sentinel-2 and Google Earth Maxar Technologies data to ensure that the imagery was clean, consistent, and ready for further analysis. More details are provided in the following subsections.

# 3.2.1. Data Preprocessing for Sentinel-2 Imagery

The images collected from Sentinel-2 for evaluating green areas in the first pipeline underwent three key preprocessing stages, as follows:

Atmospheric Correction: This step removes atmospheric effects to enhance the clarity of the imagery, ensuring that the data accurately reflects the land surface. Cloud Masking: Clouds can significantly distort measurements; therefore, this process filters out cloud cover, allowing for more accurate assessments of greenery. Spatial Alignment: To facilitate accurate comparisons and analyses, the satellite images are aligned to a consistent spatial grid, ensuring that different images can be accurately overlaid and analyzed together.

These preprocessing steps are crucial for obtaining reliable data for further analysis of urban greenery.

# 3.2.2. Data Preprocessing for Google Earth Imagery

On the other hand, the images collected from Google Earth for the purpose of counting the number of trees in the second pipeline underwent multiple preprocessing stages, as follows:

• Resizing and Tiling: This step involved standardizing image dimensions and splitting large images into smaller, manageable tiles. This approach facilitates easier processing and analysis of specific areas.   
• Adjusting Brightness and Contrast: The high-resolution images underwent processing to enhance the visibility of trees and green areas. Image enhancement techniques were employed to adjust brightness and contrast, making trees stand out clearly against the urban background. This adjustment was crucial for facilitating easier tree detection. The tool used for this image processing was Darktable [34], which allowed for precise modifications, improving the clarity and accuracy of tree identification in the urban landscape. Upscaling: We applied an upscaling method using Ultramix Balanced [35], which enhanced the resolution of the images while preserving a natural appearance. This step improved the model’s ability to detect smaller trees or clusters that may have been less noticeable in lower-resolution images. It was particularly important in urban areas where tree coverage is often sparse or fragmented.   
• Saturation Adjustment: To further enhance the model’s ability to detect trees, we increased the saturation of each image by $5 0 \%$ . This adjustment made the green areas, such as tree canopies, more vibrant and easily distinguishable. The higher saturation was especially useful for analyzing urban environments with mixed vegetation, helping the model differentiate between vegetation and surrounding urban features, thus improving detection accuracy.   
• Data Augmentation: To enhance the robustness of our tree detection model, especially in areas with sparse tree coverage, we implemented various data augmentation techniques, including the following:

Rotation: We rotated the images at 90-degree and 15-degree angles to simulate different orientations of tree canopies. This helped the model learn to recognize trees from various angles, improving its detection capabilities regardless of the

trees’ orientation in the image. Flipping: This technique exposed the model to different perspectives, aiding its ability to detect trees from both left and right orientations. 1 Hue Adjustment: We applied a $1 5 \%$ increase and decrease in hue to simulate changes in lighting and environmental conditions. This variation helped the model generalize better to different seasonal changes, weather conditions, and time-of-day lighting, thereby increasing its ability to detect trees under varying circumstances.

These preprocessing steps were essential in preparing the dataset for effective tree detection, ultimately enhancing the accuracy and reliability of the analysis in urban settings.

# 3.3. Greenery Assessment Process

Vegetation indices play a critical role in quantifying green spaces and assessing vegetation health. In this study, the Normalized Difference Vegetation Index (NDVI) [32] is used to evaluate the distribution and irrigation status of vegetation using the images taken from Sentinel-2 in our first pipeline. NDVI is calculated using the reflectance values from the near-infrared (NIR) and visible red bands of satellite imagery, which correspond to Band 8 (NIR) and Band 4 (RED) in most satellite sensors, such as Sentinel and Landsat. It is expressed as

$$
\mathrm { N D V I } = { \frac { N I R - R E D } { N I R + R E D } } .
$$

NDVI values range from $^ { - 1 }$ to $+ 1$ , where higher values indicate healthier vegetation. This index is particularly effective in distinguishing greenery areas, which is essential for tracking the progress of urban greening projects in arid regions like Riyadh. An NDVI threshold of 0.1 was set for green area detection. This value was deliberately selected to accurately reflect the unique environmental context of the study area. Since Riyadh is largely a desert region with limited natural vegetation, this threshold was chosen to identify even sparse vegetation. A lower threshold is crucial for capturing areas with low-density green cover—such as scattered trees or nascent greening projects—that a higher threshold might otherwise exclude. This inclusive methodology is essential for effectively tracking the progress of urban greening projects in arid regions like Riyadh. By ensuring that all forms of vegetation are detected, this approach allows for a more comprehensive and realistic assessment of initiatives like the “Green Riyadh Project”.

In particular, the mean NDVI was computed for each year from 2016 to 2024 to capture annual variations in green areas. This allowed the assessment of trends in vegetation over time and the determination of how green spaces had expanded or decreased across different districts of Riyadh. After calculating the mean NDVI for each year, a layout, using ArcGIS, was applied to the city of Riyadh, where it is segmented into districts. Therefore, we calculate the NDVI for each district in Riyadh. By averaging the NDVI values within the district boundaries, the data reflected the specific green area within each district, giving a more localized understanding of vegetation distribution. Specifically, we calculated the total area in $\mathrm { k m } ^ { 2 }$ , the green area in $\mathrm { k m } ^ { 2 }$ , and the percentage of green area for each district to show them later on a user-friendly dashboard.

# 3.4. Tress Counting Process

Accurately counting trees presents a significant challenge, yet it plays a crucial role in supporting urban planners and decision-makers in the strategic distribution of greenery across cities. Therefore, we adopt a transfer learning approach using the YOLOv11 model. Prior to fine-tuning the model, we partitioned our collected dataset into three subsets—training, validation, and testing—to ensure a robust and unbiased evaluation of model performance:

Training $( 7 0 \% )$ : This subset was used to train the model to recognize trees by showing it a variety of images from different districts.   
Validation $( 1 0 \% )$ : During training, we used this data to monitor the model’s performance and adjusted the hyperparameters to prevent overfitting and for earlystopping training.   
Testing $( 2 0 \% )$ : After training, we evaluated the model’s performance on this unseen data to assess how well it generalized to new images.

Object detection is a core component of this study, employed to identify and count trees within satellite imagery. The YOLOv11 [9] algorithm, an advanced version of the YOLO (You Only Look Once) series [36], is adopted for its high accuracy and efficiency in detecting objects in real time. YOLOv11 processes satellite images by predicting bounding boxes and class probabilities in a single step, ensuring the precise detection of small and densely packed objects, such as trees. This capability makes YOLOv11 an ideal choice for monitoring urban green spaces, where vegetation distribution is often non-uniform.

# 3.5. Evaluation Metrics

The primary objective of the evaluation metrics is to ensure that our methodology produces reliable and accurate results. To achieve this, we used the following key metrics:

Precision: The precision of a model is calculated to assess how many of the predicted trees were actually true positives (correct detections). The formula for precision is given by

$$
{ \mathrm { P r e c i s i o n } } = { \frac { T P } { T P + F P } } .
$$

Recall: The recall of a model is used to measure how many of the true trees in the dataset were correctly detected by the model. The formula for the recall is

$$
\mathrm { R e c a l l } = \frac { T P } { T P + F N } .
$$

F1-Score: The F1-score combines precision and recall into a single metric, balancing the trade-off between the two. The formula for the F1-score is

$$
{ \mathrm { F 1 - S c o r e } } = 2 \times { \frac { { \mathrm { P r e c i s i o n } } \times { \mathrm { R e c a l l } } } { { \mathrm { P r e c i s i o n } } + { \mathrm { R e c a l l } } } } .
$$

$\mathrm { m A P @ k }$ : The mean Average Precision (mAP) at an Intersection over Union (IoU) threshold of k is defined as

$$
\mathrm { m A P @ k } = \frac { 1 } { N } \sum _ { q = 1 } ^ { N } \mathrm { A P } _ { q } @ k .
$$

# 4. Results and Analysis

To evaluate the effectiveness of our work and the proposed dual pipeline approach, we seek to address three key research questions:

RQ1:To what extent can our methodology accurately assess the rate of greenery evolution across different districts in Riyadh?   
RQ2: Can our proposed methodology reliably quantify the number of trees within various districts?

RQ3: Can the results generated by the dual pipeline be effectively visualized in a user-friendly dashboard to support decision-making processes? The following subsections address these research questions.

# 4.1. RQ1: Rate of Greenery Evolution in Riyadh

The first pipeline in our methodology aims to evaluate an area’s greenery evolution. The NDVI analysis, which is shown in Figure 3, revealed that the rate of greenery evolution in Riyadh is not uniform across districts. While some districts, such as Al Dubiyah, showed a significant increase in green coverage $\cdot + 6 . 9 \%$ from 2016 to 2024), the overall city experienced a slight decrease in greenery by $0 . 6 7 \%$ during the same period. This suggests that, despite some areas showing improvement, many districts have seen a decline in green coverage, with 144 out of 193 neighborhoods reporting decreases in greenery. Before the launch of the “Green Riyadh” initiative in 2018, the decline in greenery was even more pronounced, at $0 . 8 6 \%$ from 2016 to 2018. However, the initiative’s announcement in 2019 appears to have had a positive effect, with a $0 . 4 5 \%$ increase in greenery from 2018 to 2020. These findings demonstrate that the rate of greenery evolution in Riyadh is influenced by both urban expansion and urban greening policies, such as the “Green Riyadh” initiative, which has had a mixed but generally positive impact on the city’s green cover.

![](images/6b885a3fcfa919267685b8778f5283896930a57e9fdf9edd8485a38017938ede.jpg)  
Figure 3. The image on the left illustrates the district layout of Riyadh, while the image on the right displays the NDVI results derived from this layout.

While urban expansion is often associated with a reduction in green spaces, the results from this study suggest that some neighborhoods in Riyadh have managed to increase green coverage despite rapid urban growth. Specifically, neighborhoods in the northern part of the city, such as the AlMalqa, AlYasmeen, and AnNarjis districts, demonstrated notable increases in greenery from 2016 to 2024. For example, AlMalqa district experienced a $2 . 6 8 \%$ increase in green coverage, and AlYasmeen district saw a $0 . 5 7 \%$ increase in green coverage, while AnNarjis had a $0 . 7 3 \%$ increase in green coverage. These findings suggest that, contrary to the typical expectation of green space loss with urban expansion, some districts have successfully integrated greenery into urban development. This partially validates the hypothesis, indicating that urban expansion can be managed in such a way that it not only preserves but also enhances green areas, provided that strategic urban planning and green space initiatives are prioritized.

# 4.2. RQ2: Number of Trees in Riyadh

The second pipeline in our methodology focuses on accurately counting the number of trees within a designated area. To achieve this, we fine-tuned several object detection models, including YOLOv7 [7], YOLOv8 [8], YOLOv11 [9], and RT-DERT [10]. The performance results presented in Table 2 illustrate the effectiveness of these models, with YOLOv11 achieving a mean Average Precision $( \mathbf { m A P @ 5 0 } )$ of $9 5 . 4 \%$ a mAP $\textcircled { \omega } 5 0 \textcircled { - } 9 5$ of $7 1 . 7 \%$ , a precision of $9 4 . 6 \%$ , and a recall of $9 0 . 2 \%$ . While RT-DERT delivers competitive results comparable to YOLOv11, its significantly slower processing time makes YOLOv11 the superior choice. These findings provide valuable insights into the effectiveness of our model selection for tree detection.

Table 2. A comparative analysis of several fine-tuned deep learning models was conducted using our test dataset. The best-performing results are underlined.   

<html><body><table><tr><td>Model</td><td>Precision</td><td>Recall</td><td>F1-Score</td><td>mAP@50</td><td>mAP@50-95</td></tr><tr><td>YOLOv7</td><td>89.5%</td><td>84.5%</td><td>86.9%</td><td>90.1%</td><td>53.8%</td></tr><tr><td>YOLOv8</td><td>90.5%</td><td>84.6.2%</td><td>87.4%</td><td>90.1%</td><td>63.4%</td></tr><tr><td>RT-DETR</td><td>93.6%</td><td>91.4%</td><td>92.5%</td><td>94.7%</td><td>70.2%</td></tr><tr><td>YOLOv11</td><td>94.6%</td><td>90.2%</td><td>92.3%</td><td>95.4%</td><td>71.7%</td></tr></table></body></html>

For instance, in the AlSalam district, our model detects a total of 1651 trees in this area. The images from this district showcased the model’s ability to identify trees in a relatively less urbanized environment, with varying tree densities and sizes. In another example from King Saud University (KSU) district, a much larger number of trees were detected, with 10,104 trees identified. This higher count can be attributed to the larger area covered by the university, which includes extensive green spaces, campuses, and surrounding landscapes, providing a higher tree density compared to other districts. Sample results from the model detection are shown in Figure 4.

![](images/386368d1917ae34430532b0828c2883f76e18f51ba3822867ffd598a9ec77ea1.jpg)  
Figure 4. Representative sample images generated from the predictions of our fine-tuned YOLOv11 on our test subset. The visualizations serve to qualitatively assess the performance of our model and provide insights into its strengths and potential areas for improvement.

# 4.3. RQ3: Designing User-Friendly Dashboard

Our findings are not just theoretical—we have applied them to a real-world scenario and made them accessible. Figure 5 displays samples of the results on a user-friendly dashboard, making it easy for urban planners and environmental managers to obtain the insights they need.

![](images/38d09be21aa80d3a7e2974d4cee646e65455ae580a0dfadb86cadc56aac6fe8f.jpg)  
Figure 5. Representative screenshots captured from the deployed dashboard interface. These visuals demonstrate the layout, core functionalities—such as total green area $( \mathrm { i n } \mathrm { k m } ^ { 2 } \cdot$ ), percentage of green coverage, tree counts, temporal change percentages, and district rankings based on green area—and interactive features including district and year selection.

To ensure the results of our analysis are accessible and actionable for stakeholders, we developed an interactive dashboard using Power BI. This dashboard serves as a decision support tool, translating the complex data from our dual pipeline methodology into clear and intuitive visualizations. The dashboard is designed to be highly interactive, allowing users such as urban planners and environmental managers to perform spatio-temporal analysis by filtering the data based on specific districts and years. This functionality enables the dynamic visualization of key metrics, including the total green area, the percentage of green coverage, and the quantified number of trees, which update in response to user selections. The system’s architecture populates the dashboard with data processed through our analytical pipelines. Green area coverage is calculated via NDVI analysis on Sentinel-2 imagery, a process conducted on the Google Earth Engine platform. Concurrently, tree counts are quantified from high-resolution Google Earth Maxar Technologies imagery using our custom-trained YOLOv11 model. The resulting aggregated data is then loaded into Power BI for visualization. While the current implementation operates on periodically updated offline data, the modular nature of the pipeline is designed to be extensible and could be adapted in the future to ingest live data feeds for near real-time monitoring. This approach makes our findings readily available and empowers data-driven decision-making for urban greening initiatives.

# 5. Conclusions

This study introduces a novel and practical approach for assessing urban green spaces by integrating high-resolution satellite imagery with the YOLOv11 object detection model, significantly enhancing green space evaluation’s accuracy, scalability, and reliability compared to traditional methods. Applied within the context of the “Green Riyadh” initiative, the proposed methodology effectively quantifies vegetation density and identifies tree counts and distributions, serving as a critical tool for environmental monitoring and datadriven urban planning. The findings highlight the potential of the proposed dual pipeline to support large-scale ecological initiatives through continuous and objective progress tracking toward sustainability goals. Designed to be both adaptable and transferable, the methodology offers a robust framework applicable to cities worldwide facing similar urban sustainability challenges.

Despite the use of advanced object detection models, not all trees were successfully identified in the satellite imagery. Several factors contribute to this limitation. First, visibility challenges—such as trees appearing semi-transparent or being partially obscured by buildings, shadows, or other vegetation—can hinder accurate detection. Second, smaller trees, particularly saplings or shrubs, may fall below the model’s detection threshold due to their limited size and lower visual prominence.

A key direction for future work is acquiring very high-resolution (VHR) satellite imagery to enhance tree detection accuracy. VHR images, with resolutions finer than $3 0 \mathrm { c m } ,$ will provide more detailed data, allowing for better identification of trees, especially in densely built urban areas. This will improve the model’s ability to detect smaller or obscured trees and refine our understanding of urban greenery.

Author Contributions: Conceptualization, M.A.; methodology, M.A.; software, A.A. (Abdullah Alahmad), A.A. (Abdalrahman Alalwan), and F.A.; validation, M.A., A.A. (Abdullah Alahmad), and A.A. (Abdalrahman Alalwan); formal analysis, A.A. (Abdullah Alahmad) and A.A. (Abdalrahman Alalwan); investigation, M.A.; resources, M.A.; data curation, A.A. (Abdullah Alahmad) and A.A. (Abdalrahman Alalwan); writing—original draft preparation, M.A.; writing—review and editing, A.A. (Abdullah Alahmad), A.A. (Abdalrahman Alalwan), and F.A.; visualization, A.A. (Abdullah Alahmad).; supervision, M.A.; project administration, M.A.; funding acquisition, M.A. All authors have read and agreed to the published version of the manuscript.

Funding: This research is funded by the Ongoing Research Funding Program (ORF-2025-1225), King Saud University, Riyadh, Saudi Arabia.

Data Availability Statement: The data presented in this study is available on request from the corresponding author.

Acknowledgments: The authors extend their appreciation to King Saud University for funding this research through the Ongoing Research Funding Program (ORF-2025-1225), King Saud University, Riyadh, Saudi Arabia.

Conflicts of Interest: The authors declare no conflicts of interest.

# References

1. Paudel, S.; States, S.L. Urban green spaces and sustainability: Exploring the ecosystem services and disservices of grassy lawns versus floral meadows. Urban For. Urban Green. 2023, 84, 127932. [CrossRef]   
2. Saudi Vision 2030. Green Riyadh. Available online: https://www.vision2030.gov.sa/en/explore/projects/green-riyadh (accessed on 12 January 2025).   
3. Riyadh Development Authority. Green Riyadh Initiative. Available online: https://www.rcrc.gov.sa/en/projects/green-riyadhproject (accessed on 12 January 2025).   
4. Ajaz, A.; Karimi, P.; Cai, X.; De Fraiture, C.; Akhter, M.S. Statistical data collection methodologies of irrigated areas and their limitations: A review. Irrig. Drain. 2019, 68, 702–713. [CrossRef]   
5. Ahmed, Z.; Gui, D.; Murtaza, G.; Yunfei, L.; Ali, S. An overview of smart irrigation management for improving water productivity under climate change in drylands. Agronomy 2023, 13, 2113. [CrossRef]   
6. Amir, A. Counting Trees Using Satellite Images. GitHub Repository. 2021. Available online: https://github.com/A2Amir/ Counting-Trees-using-Satellite-Images (accessed on 1 May 2025).   
7. Wang, C.Y.; Bochkovskiy, A.; Liao, H.Y.M. YOLOv7: Trainable bag-of-freebies sets new state-of-the-art for real-time object detectors. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, Vancouver, BC, Canada, 17–24 June 2023; pp. 7464–7475.   
8. Varghese, R.; Sambath, M. Yolov8: A novel object detection algorithm with enhanced performance and robustness. In Proceedings of the 2024 International Conference on Advances in Data Engineering and Intelligent Computing Systems (ADICS), Chennai, India, 18–19 April 2024; pp. 1–6.   
9. Khanam, R.; Hussain, M. Yolov11: An overview of the key architectural enhancements. arXiv 2024, arXiv:2410.17725.   
10. Zhao, Y.; Lv, W.; Xu, S.; Wei, J.; Wang, G.; Dang, Q.; Liu, Y.; Chen, J. Detrs beat yolos on real-time object detection. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, Seattle, WA, USA, 16–22 June 2024; pp. 16965–16974.   
11. Dare, P.M. Shadow analysis in high-resolution satellite imagery of urban areas. Photogramm. Eng. Remote Sens. 2005, 71, 169–177. [CrossRef]   
12. Asokan, A.; Anitha, J.; Ciobanu, M.; Gabor, A.; Naaji, A.; Hemanth, D.J. Image processing techniques for analysis of satellite images for historical maps classification—An overview. Appl. Sci. 2020, 10, 4207. [CrossRef]   
13. Verbesselt, J.; Zeileis, A.; Herold, M. Near real-time disturbance detection using satellite image time series. Remote Sens. Environ. 2012, 123, 98–108. [CrossRef]   
14. Gadamsetty, S.; Ch, R.; Ch, A.; Iwendi, C.; Gadekallu, T.R. Hash-based deep learning approach for remote sensing satellite imagery detection. Water 2022, 14, 707. [CrossRef]   
15. Yarlagadda, S.K.; Güera, D.; Bestagini, P.; Zhu, F.M.; Tubaro, S.; Delp, E.J. Satellite image forgery detection and localization using gan and one-class classifier. arXiv 2018, arXiv:1802.04881. [CrossRef]   
16. Siok, K.; Ewiak, I.; Jenerowicz, A. Multi-sensor fusion: A simulation approach to pansharpening aerial and satellite images. Sensors 2020, 20, 7100. [CrossRef]   
17. Bai, C.; Zhao, D.; Zhang, M.; Zhang, J. Multimodal information fusion for weather systems and clouds identification from satellite images. IEEE J. Sel. Top. Appl. Earth Obs. Remote Sens. 2022, 15, 7333–7345. [CrossRef]   
18. Iglovikov, V.; Mushinskiy, S.; Osin, V. Satellite imagery feature detection using deep convolutional neural network: A kaggle competition. arXiv 2017, arXiv:1706.06169.   
19. Al-Ghrairi, A.H.T.; Abed, Z.H.; Fadhil, F.H.; Naser, F.K. Classification of satellite images based on color features using remote sensing. Int. J. Comput. IJC 2018, 31, 42–52.   
20. Sladojevic, S.; Arsenovic, M.; Anderla, A.; Culibrk, D.; Stefanovic, D. Deep neural networks based recognition of plant diseases by leaf image classification. Comput. Intell. Neurosci. 2016, 2016, 3289801. [CrossRef]   
21. Sibiya, M.; Sumbwanyambe, M. A computational procedure for the recognition and classification of maize leaf diseases out of healthy leaves using convolutional neural networks. AgriEngineering 2019, 1, 119–131. [CrossRef]   
22. He, T.; Zhou, H.; Xu, C.; Hu, J.; Xue, X.; Xu, L.; Lou, X.; Zeng, K.; Wang, Q. Deep learning in forest tree species classification using sentinel-2 on google earth engine: A case study of Qingyuan County. Sustainability 2023, 15, 2741. [CrossRef]   
23. Sun, Y.; Liu, Y.; Wang, G.; Zhang, H. Deep learning for plant identification in natural environment. Comput. Intell. Neurosci. 2017, 2017, 7361042. [CrossRef]   
24. Huixian, J. The analysis of plants image recognition based on deep learning and artificial neural network. IEEE Access 2020, 8, 68828–68841. [CrossRef]   
25. Bodhwani, V.; Acharjya, D.P.; Bodhwani, U. Deep residual networks for plant identification. Procedia Comput. Sci. 2019, 152, 186–194. [CrossRef]   
26. Kanda, P.S.; Xia, K.; Kyslytysna, A.; Owoola, E.O. Tomato leaf disease recognition on leaf images based on fine-tuned residual neural networks. Plants 2022, 11, 2935. [CrossRef]   
27. Zhao, Z.; Chen, S.; Ge, Y.; Yang, P.; Wang, Y.; Song, Y. Rt-detr-tomato: Tomato target detection algorithm based on improved rt-detr for agricultural safety production. Appl. Sci. 2024, 14, 6287. [CrossRef]   
28. Wang, S.; Jiang, H.; Yang, J.; Ma, X.; Chen, J.; Li, Z.; Tang, X. Lightweight tomato ripeness detection algorithm based on the improved RT-DETR. Front. Plant Sci. 2024, 15, 1415297. [CrossRef] [PubMed]   
29. Hu, J.; Zheng, J.; Wan, W.; Zhou, Y.; Huang, Z. RT-DETR-EVD: An Emergency Vehicle Detection Method Based on Improved RT-DETR. Sensors 2025, 25, 3327. [CrossRef]   
30. Wang, Z.; Lei, L.; Li, T.; Zu, X.; Shi, P. RT-DETR-Smoke: A Real-Time Transformer for Forest Smoke Detection. Fire 2025, 8, 170. [CrossRef]   
31. Phiri, D.; Simwanda, M.; Salekin, S.; Nyirenda, V.R.; Murayama, Y.; Ranagalage, M. Sentinel-2 data for land cover/use mapping: A review. Remote Sens. 2020, 12, 2291. [CrossRef]   
32. Bhandari, A.K.; Kumar, A.; Singh, G.K. Feature extraction using Normalized Difference Vegetation Index (NDVI): A case study of Jabalpur city. Procedia Technol. 2012, 6, 612–621. [CrossRef]   
33. Maxar Technologies, WorldView-3: Next-Generation High-Resolution Commercial Satellite Imagery. Available online: https://resources.maxar.com/data-sheets/worldview-3 (accessed on 1 May 2025).   
34. Darktable. Darktable Main Repository. Available online: https://www.darktable.org/ (accessed on 1 May 2025).   
35. Upscayl. Upscayl GitHub Repository. Available online: https://github.com/upscayl/upscayl (accessed on 1 May 2025).   
36. Redmon, J.; Divvala, S.; Girshick, R.; Farhadi, A. You only look once: Unified, real-time object detection. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, Las Vegas, NV, USA, 27–30 June 2016; pp. 779–788.

Disclaimer/Publisher’s Note: The statements, opinions and data contained in all publications are solely those of the individual author(s) and contributor(s) and not of MDPI and/or the editor(s). MDPI and/or the editor(s) disclaim responsibility for any injury to people or property resulting from any ideas, methods, instructions or products referred to in the content.