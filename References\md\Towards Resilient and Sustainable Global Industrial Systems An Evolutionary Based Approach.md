Towards Resilient and Sustainable Global Industrial Systems: An Evolutionary  
Based Approach   
V<PERSON><PERSON><PERSON>, Jirˇı´ <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,   
<PERSON>

#

![](images/fb6bb01fc78a456468cd1bdddb668fe463b94f412c238e126c1f93c2a479d5b0.jpg)  
Automatic Generation of Resilient Industrial System Using Evolutionary-Based Approach

#

![](images/094b7c05e90b9c9a0a125e4fafdeaeba6748ace36ccd136afd56c1a51aa06955.jpg)

![](images/de033c335e0bb2f0415515b432830a3a2e6a21f212f62c03b153b01b408e74a2.jpg)

#

# Highlights

# Towards Resilient and Sustainable Global Industrial Systems: An EvolutionaryBased Approach

V´<PERSON><PERSON>, Jirˇı´ <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>

• The formalization of an optimization problem for global industrial system design. Optimization integrating an evolutionary algorithm and mathematical programming.   
• Use of OWL ontology for data consistency and constraint management.

# Towards Resilient and Sustainable Global Industrial Systems: An Evolutionary-Based Approach

V´aclav Jirkovsky´ $^ { \mathrm { a } , \ast }$ , Jiˇrı´ Kubal´ık $\mathbf { a }$ , Petr Kadera $\mathbf { a }$ , Arnd Schirrmann $^ \mathrm { b }$ , Andreas Mitschke $^ \mathrm { b }$ , Andreas Zindel $^ \mathrm { b }$

$a$ Czech Institute of Informatics, Robotics and Cybernetics, Czech Technical University in Prague, Prague, 16000, Czech Republic $b$ Airbus Central R&T, Germany

# Abstract

This paper presents a new complex optimization problem in the field of automatic design of advanced industrial systems and proposes a hybrid optimization approach to solve the problem. The problem is multi-objective as it aims at finding solutions that minimize CO $^ 2$ emissions, transportation time, and costs. The optimization approach combines an evolutionary algorithm and classical mathematical programming to design resilient and sustainable global manufacturing networks. Further, it makes use of the OWL ontology for data consistency and constraint management. The experimental validation demonstrates the effectiveness of the approach in both single and double sourcing scenarios. The proposed methodology, in general, can be applied to any industry case with complex manufacturing and supply chain challenges.

Keywords: multi-objective optimization, evolutionary algorithm, ontology, industrial system, supply chain

# 1. Introduction

This paper presents the results of research work performed at a large European aircraft manufacturer that has focused on the capabilities development and verification for the design of novel, disruptive manufacturing systems for future aircraft programs.

The production of commercial aircraft is a complex and highly distributed process. For example, the Airbus A380 comprises 2.5 million individual parts, produced by 1500 companies in 30 countries worldwide (Airbus, 2021). Due to the need for highly specialized parts and strict certification requirements (concerning safety and dual use) production facilities for these parts cannot be created on a green field. Rather these parts are sourced from established, advanced manufacturers both from company owned subsidiaries and external suppliers. Thus both the optimization of production location allocations and transportation of the produced parts is pivotal to the performance of the industrial system for creating such complex products.

Efficient transportation of the larger parts of an aircraft is crucial. Major components (MCs) such as wings, engines, sections, etc. cannot be transported with regular shipping methods. The aforementioned A380 has a wing span of almost 79.8 m with an overall length of 72.7 m and a fuselage diameter of $7 . 1 \mathrm { m }$ (Airbus, 2021). In comparison a standard 40 ft container with dimensions of $1 2 . 1 9 \mathrm { m }$ length, 2.44 m width, 2.59 m height is only suitable for smaller parts of the aircraft. Given that some parts can only be manufactured at specific locations puts major constraints onto the design of a global industrial system architecture.

To transport these major components, special transportation means are required (oversized road transporters with or without obligatory escorts, large transport aircraft and unique cargo ships). These transportation means are costly; monetary but also with regards to other resources such as lead time and CO2 emissions.

An optimal industrial system would not only minimise these KPIs but also ensure that all other constraints are kept in check. Such as the availability of suppliers for special parts, sourcing the same part from different suppliers (so called double sourcing) to increase the systems resilience or keep boundaries for the allotted workshare of suppliers in check to minimize overdependencies.

Optimizing such complex system topologies manually traditionally relies heavily on heuristics (trial and error, know-how, common sense) which usually fall short of achieving an optimal solution (Aickelin and Clark, 2011). However, due to the high dimensional complexity of the combinatorial problem, brute forcing an optimal solution is computationally expensive (Karp, 1975).

In order to provide a solid baseline for the exploration of this optimization problem, an industrial system data model was developed (Dietz et al., 2023). The model was built using Web Ontology Language (OWL), which was identified as a suitable machine-readable format for various industrial problems, from zero-defect manufacturing to supply chain management (Zhao and Liu, 2008; Psarommatis et al., 2023; Scheuermann and Leukel, 2014). The model contains all possibilities for valid variants of the industrial system such as the product breakdown structure, production locations, transportation means, workshare constraints and valid network routes. This was done so that inconsistencies could be rectified more easily, efficiently share data with the optimization algorithm and - most importantly - to reduce the number of combinations only providing valid options to the optimizer.

This paper focuses on the optimization of the industrial system architecture, in particular the topology of production locations, sourcing strategies, and transport connections within these production networks. The optimization process is split into two phases. The first phase, phase I, uses an evolutionary algorithm (EA) to create an optimized industrial system topology based on predefined constraints. The second phase, phase II, represents transportation optimization together with batching optimization.

To verify the performance of the proposed optimization framework, real data from a specific case study of industrial system architecture design, provided by an involved aircraft manufacturer, was used. To the best of our knowledge, no study addressing a similar problem of this complexity has yet been described in the literature.

The main contributions of this paper are:

• A new complex optimization problem for automatically designing industrial system architectures has been formulated. It involves challenges that every manufacturing company with a large network of manufacturing facilities and suppliers scattered across different countries faces today.   
• An approach combining an evolutionary algorithm, formal mathematical programming methods, and an ontology-based data model was designed to solve the proposed optimization problem.   
• Exploitation of OWL ontology to keep the knowledge base consistent, providing only relevant resources according to the ontology axioms.

This paper is organized as follows: First, a detailed problem description is provided in Section 2. Next, the main concepts of the proposed method are described in Section 3. Section 4 presents a survey of related work. Experiments and their discussion are presented in Section 5. Finaly, Section 6 concludes the paper.

# 2. Problem Definition

This section provides a formal definition of the problem addressed in this work. The problem involves the design of an optimal industrial system architecture with respect to the overall transportation costs, subject to a number of constraints.

Following is a list of entities that appear in the problem definition:

• Production graph $G$ – the manufacturing process of the final product can be formally described by a tree, where the root node represents the final product, the leaf nodes are atomic, further indivisible, parts of the product, and the internal nodes represent composite parts assembled from the parts that are at the input to the internal node.

• $P - \mathrm { a }$ set of $M$ parts, $\{ p _ { 1 } , . . . , p _ { M } \}$ , present in the production graph $G$ , i.e., the final product, all composite parts, and all atomic parts.

• $L - \mathrm { a }$ set of production site locations; in each location, one or more production plants are operated.   
• $C$ – a set of countries where the production sites are located.   
• $F$ – a set of production plants (factories), where each production plant is assigned to a particular production site $l \in L$ in one specific country $c \in C$ . Each plant can produce one or more types of parts. The set of parts producible by the plant $f$ is denoted as $P ^ { f } \subset P$ .   
• $S$ – a set of suppliers that can produce all elements of graph $G$ . A supplier operates production plants at several production sites (one plant at a site) in various countries.   
• $U$ – a set of production units $U$ where each production unit is a unique pair of a particular supplier $s \in S$ and one of its production plants $f \in F$ .   
• $U ^ { p } \mathrm { ~ - ~ a ~ }$ subset of production units $U ^ { p } \subset U$ such that each $u \in U ^ { p }$ can produce the particular part $p \in P$ .   
• $U ^ { s } \mathrm { ~ - ~ a ~ }$ subset of production units $U ^ { s } \subset U$ that belong to the supplier $s \in S$ .   
• $U ^ { c } - \mathrm { a }$ subset of production units $U ^ { c } \subset U$ that are located in the country $c \in C$ .   
• $W \mathrm { ~ - ~ a ~ }$ set of warehouses that serve as storage for input/output parts for/from several production plants.   
• $W ^ { u } - \mathrm { a }$ subset of warehouses $W ^ { u } \subset W$ that are available to the production unit $u \in U$ .   
• $v ^ { p } - \mathrm { a }$ value-added $v ^ { p }$ is a real number from the interval (0, 1) that represents the ratio of the value of the part $p \in P$ to the total value of the final product.   
• $v ^ { c } - \mathrm { a }$ value-added $v ^ { c }$ is a real number from the interval $( 0 , 1 )$ that represents the actual value-added allocated to the country $c \in C$ calculated as the ratio of the sum of values of all parts produced in production units $u \in U ^ { c }$ to the total value of the final product.   
• $v ^ { s } - \mathrm { a }$ value-added $v ^ { s }$ is a real number from the interval $( 0 , 1 )$ that represents the actual value-added allocated to the supplier $s \in S$ calculated as the ratio of the sum of values of all parts produced in production units $u \in U ^ { s }$ to the total value of the final product. $v _ { m i n } ^ { c }$ , $v _ { m a x } ^ { c }$ – required minimum and maximum boundaries on the value-added allocated to the country $c \in C$ .   
• $v _ { m i n } ^ { s }$ , $v _ { m a x } ^ { s }$ – required minimum and maximum boundaries on the value-added allocated to the supplier $s \in S$ .   
• $v _ { m a x } ^ { u } \mathrm { ~ - ~ t h e ~ }$ maximum added-value allowed for the production unit $u \in U$ .   
• $T$ – a set of possible means of transportation that can be used to transport the parts. Each type of transportation $t \in T$ has specified its CO $_ 2$ emission $t _ { c }$ , speed $t _ { s }$ , cost $t _ { t }$ , and maximum load volume $t _ { l }$ .   
• $\mathcal { G } ^ { T } ( \mathcal { N } , \mathcal { E } )$ – transportation graph is a directed graph in which all possible instances of the industrial system can be represented. Its nodes, $\mathcal { N }$ , comprise all production units and all available warehouses, i.e., ${ \mathcal { N } } = U \cup W$ . The edges in the set $\mathcal { E }$ represent transportation links used to carry a load from the source node to the destination node, i.e., an edge is an oriented link $( u , v )$ where $u , v \in \mathcal { N }$ . Each edge $\varepsilon \in \mathcal { E }$ can only choose its transportation type $\tau ^ { \varepsilon }$ from the set of possible transportation types $T ^ { \varepsilon } \subset T$ available for the edge. The chosen transportation type determines the edge’s length $l ^ { \varepsilon }$ . Further, each edge has its batch size $b ^ { \varepsilon }$ , which is the number of parts transported in a single

shipment along the edge. Here, we consider a single-product batching consisting of one or more pieces of the same part. Figure 2 illustrates the most important components of the transportation graph.

• $I ( N , E )$ – a directed graph as a sub-graph of the transportation graph representing a particular industrial system designed to produce the final product. Its nodes $N$ and edges $E$ are a subset of $\mathcal { N }$ and $\mathcal { E }$ , respectively. Every shortest path in terms of the number of edges between two nodes $u _ { 1 }$ $\iota _ { 1 } , u _ { 2 } \in U$ must have the following structure $u _ { 1 }  w ^ { * }  u _ { 2 }$ , where $w ^ { * }$ means that there can be zero or more warehouses $w \in W$ used on the path. Each edge $e \in E$ has already assigned its particular type of transportation that determines its length $l ^ { e }$ . It has also specified its batch size $b ^ { e }$ . Each production unit node $u \in N \cap U$ is assigned one or more specific parts chosen from its production plant’s set of producible parts. In Figure 1, the blue tiles represent production units with its supplier $s$ and production plant $f$

In the case of double sourcing, each part has to be produced in two different production units while splitting the whole production of the part between the two production units in the ratio $a : ( 1 - a )$ where $0 < a < 1$ , see Figure 1. The particular value of the production split is defined for all parts and respective production units of the industrial system.

The assignment of parts to production units and the production split definitions are denoted as a production assignment of the industrial system.

# 2.1. Constraints

The constraints imposed on a valid solution used in this study are defined below.

• Constraint 1 – a part shall only be manufactured by a production unit with the manufacturing capability for said part.

![](images/5702fb4a0a0a748cdeda16e39564ccc31fcd3554b369c25e1dfe99d7e4df992a.jpg)  
Fig. 1. An illustrative example of a part of the production assignment realizing the production of Section 6 from two source parts, S6 Lower Shell and S6 Upper Shell, where the double sourcing is required.

• Constraint 2 – in the case of double sourcing, the two production units producing the same part shall be located in two different countries if applicable. Note that some parts can be produced only in production units where all of them are located in the same country. In such a case, this constraint is not enforced.   
• Constraint 3 – in the case of double sourcing, the split between production units producing the same part shall be between 20% to 80%, i.e., $0 . 2 \leq a \leq 0 . 8$ .   
• Constraint 4 – the value added by the country $c$ is less than or equal to $v _ { m a x } ^ { c }$ for all $c \in C$ .   
• Constraint 5 – the value added by the supplier $s$ is less than or equal to $v _ { m a x } ^ { s }$ for all $s \in S$ .   
• Constraint 6 – the value added by the production unit $u$ is less than or equal to $v _ { m a x } ^ { u }$ for all $u \in U$ .   
• Constraint 7 – the batch size $b ^ { e }$ assigned to every edge $e$ of the industrial system $I ( N , E )$ can not be larger than the maximum possible value, which is

determined by the maximum load’s volume $t _ { l }$ of the transportation type assigned to the edge and the size of the part transported along the edge.

Some constraints are relevant only for phase I and phase II, respectively; see below in Section 3.2 and Section 3.3.

# 2.2. Optimization objectives

When designing the optimal industrial system, we consider the following optimization objectives:

• Minimize the total amount of CO $2$ generated while using the industrial system to produce $K$ pieces of the final product. Formally, this objective is defined as

$$
\operatorname * { a r g m i n } _ { I ( E , N ) \in \mathcal { G } ^ { \mathcal { T } } ( \mathcal { E } , \mathcal { N } ) } f _ { 1 } ( I ( E , N ) ) = \sum _ { e \in E } n _ { e } * l ^ { e } * \tau _ { c } ^ { e }
$$

where $n _ { e }$ is the number of rides carried out along the edge $e$ of the length $l ^ { e }$ while using its assigned transportation type $\tau ^ { e }$ and emitting $\tau _ { c } ^ { e }$ grams of CO $^ 2$ per kilometer.

• Minimize the total transportation time spent while using the industrial system to produce $K$ pieces of the final product. Formally, this objective is defined as

$$
\operatorname * { a r g m i n } _ { I ( E , N ) \in \mathcal { G } ^ { T } ( \mathcal { E } , \mathcal { N } ) } f _ { 2 } ( I ( E , N ) ) = \sum _ { e \in E } n _ { e } * \frac { l ^ { e } } { \tau _ { s } ^ { e } }
$$

where $n _ { e }$ is the number of rides carried out along the edge $e$ of the length $l ^ { e }$ km using the transportation type $\tau ^ { e }$ with the speed of $\boldsymbol { \tau _ { s } ^ { e } }$ km/hour.

• Minimize the total transportation distance while using the industrial system to produce $K$ pieces of the final product. Formally, this objective is defined as

$$
\underset { I ( E , N ) \in \mathcal { G } ^ { T } ( \mathcal { E } , \mathcal { N } ) } { \mathrm { a r g } \mathrm { m i n } } f _ { 3 } ( I ( E , N ) ) = \sum _ { e \in E } n _ { e } \ast l ^ { e }
$$

where $n _ { e }$ is the number of rides carried out along the edge $e$ of the length $l ^ { e }$ km using the transportation type $\tau ^ { e }$ .

• Minimize the total transportation costs while using the industrial system to produce $K$ pieces of the final product. Formally, this objective is defined as

$$
\operatorname * { a r g m i n } _ { I ( E , N ) \in \mathcal { G } ^ { \mathcal { T } } ( \mathcal { E } , \mathcal { N } ) } f _ { 4 } ( I ( E , N ) ) = \sum _ { e \in E } n _ { e } * l ^ { e } * \tau _ { t } ^ { e }
$$

where $n _ { e }$ is the number of rides carried out along the edge $e$ of the length $l ^ { e }$ km using the transportation type $\tau ^ { e }$ with the transportation cost of $\tau _ { t } ^ { e }$ euro/km.

# 3. Method

# 3.1. Knowledge Base

This section introduces the benefits of the knowledge base which is exploited by phase I and phase II. Detailed information about the knowledge base is described in (Dietz et al., 2023) as mentioned in the introduction section.

Input data are specified within Web Ontology Language (OWL) ontology (Antoniou and Harmelen, 2009). This formalism offers the possibility to utilize not only data but also knowledge as machine-readable. The machine readability denotes mainly semantics added into the dataset, i.e., specified relations between entities, cardinality of properties, and other axioms in general.

The important benefits of OWL ontologies are the exploitation of Semantic Web Rule Language (SWRL) (Horrocks et al., 2004) and automated reasoning (Shearer et al., 2008). SWRL to move from Open World Assumption (Drummond and Shearer, 2006; Wang and Yu, 2014) paradigm of OWL to the closed world. Furthermore, applications for this ontology are described in (Dietz et al., 2023). Next, reasoning tasks are the following:

Satisfiability of a concept - if the concept definition is not contradictory. • Subsumption of concepts - if concept C subsumes concept D. Consistency of ABox with respect to TBox (De Giacomo et al., 1996) - if individuals in ABox do not violate axioms defined by TBox.

• Check an individual - if an individual is an instance of a concept.

Retrieval of individuals - all individuals of a concept.

• Realization of an individual - all concepts specifying the individual.

In this work, the consistency check reasoning task is exploited. Not only to maintain data to be in required shape, but also to check if a product produced by a production unit has an appropriate transportation resource. I.e., whether the bounding box of a part fits into a container.

# 3.2. Phase I

This section describes the phase I part of the proposed method, i.e., the EA designed to search for the best production assignment of the industrial system. We chose an evolutionary algorithm because these algorithms have proven to be highly suitable for solving complex real-world constrained optimization problems; see Park and Kim (2016); Chai et al. (2025); Li et al. (2025). First, the optimization objectives and constraints relevant to this phase are listed. Then, the EA’s main components and working schema are described.

# 3.2.1. Optimization objectives and constraints

In this phase, out of all constraints defined in Section 2.1, only constraints $1 ^ { - 6 }$ are considered. Constraint 7 is irrelevant since only the production assignment is optimized here. The optimization of transportation utilizing the information about available transportation means, possible batching strategies, takt time, the required number of products to be produced per month, etc., is left for the phase II.

The EA uses two performance measures listed below, ranked by priority:

• Primary objective – maximize the satisfaction ratio, sat, defined as the ratio of the number of parts successfully added to the production assignment to the total number of parts. Successful insertion of parts into the production assignment means that the parts were added to the production assignment without violating any of the relevant constraints; see below in Section 3.2.2.

• Secondary objective – minimize the overall transportation distance, dist, defined as the sum of distances between all pairs of the source and destination production units involved in the production assignment. For a particular edge $\varepsilon = ( u _ { 1 } , u _ { 2 } )$ , where $\varepsilon \in \mathcal { E }$ and $u _ { 1 } , u _ { 2 } \in U$ , the maximum distance over all possible distances defined by the set of transportation types available for the edge, $\tau ^ { \varepsilon } \in \mathcal { I } ^ { \varepsilon }$ , is considered. This upper bound value is used since it is unknown at this stage what transportation means will be used to transport products from the unit $u _ { 1 }$ to the unit $u _ { 2 }$ . Thus, the measure serves as a rough estimate of transportation costs. It is calculated only for a feasible solution with $s a t = 1$ .

# 3.2.2. Evolutionary Algorithm

This section describes the main components of the EA designed to generate a valid production assignment of the industrial system.

Solution representation. The EA implemented in this work adopts the indirect representation of solutions introduced in (Kubal´ık et al., 2019). In this representation, the exact assignment of parts to production units is not explicitly encoded in the solution representation. Instead, the solution is represented using a list of parts, denoted as priority list, which determines the order in which individual parts will be inserted into the production assignment.

When evaluating the quality of a particular candidate solution, its priority list must be first translated into the production assignment via an iterative mapping process. It starts with an empty production assignment to which new parts are gradually added one by one in the order defined by the priority list. When adding a new part $p$ to the current production assignment, the set of available production units is first initialized, starting with the set of all production units that can produce the part, $U ^ { p }$ , and filtering out those units that already violate some constraint. If the set of available production units is not empty, one of them is randomly chosen, preferably among the unused ones (i.e., those not assigned to produce any part yet). Otherwise, no production unit can be assigned to the part, and the mapping process terminates. Thus, the mapping procedure ends when all parts have been successfully incorporated into the production assignment or when a part that cannot be added to the production assignment has been reached. The former means that a complete and valid production assignment has been constructed. The latter means that the priority list does not represent a valid solution.

Genetic operators. Here, we adopt a variation of the 1-point crossover operator. It randomly selects one crossover point, inherits the head section from the first parent’s priority list, and fills in the remaining missing elements in the order they appear in the second parent’s priority list. The mutation operator operates on a single parent’s priority list so that it changes the position of a randomly chosen part within the priority list.

A standard tournament selection is used, which applies the following rule to decide if the solution $s _ { 1 }$ is better than the solution $s _ { 2 }$ :

$$
( s a t ( s _ { 1 } ) = s a t ( s _ { 2 } ) \mathbf { a n d } d i s t ( s _ { 1 } ) \leq d i s t ( s _ { 2 } ) )
$$

then return true

This rule puts pressure towards production assignments, which are valid and induce a small overall transportation distance. Note, the dist metric does not consider possibilities to transport products via warehouses. This is left for the second optimization phase. It is important to note that the validity of the solution, whether complete or incomplete, is ensured by the proposed method for mapping the priority list to the production assignment. Convergence to complete valid solutions is driven by the selection rule.

Evolutionary model. The algorithm evolves a population of candidate solutions using the selection, crossover, and mutation operators described above. The outline

1 gen 0 2 pop.init() 3 while $g e n < G$ do 4 $g e n \gets g e n + 1$ 5 $i n t e r P o p \gets \{ \}$ 6 while interP op.size() $<$ pop.size() do 7 $p a r e n t _ { 1 } \gets p o p$ .selection() 8 $p a r e n t _ { 2 } \gets p o p .$ .selection() 9 if random $( ) < p _ { c }$ then 10 $c h i l d \gets p a r e n t _ { 1 }$ .mate(parent2) 11 if random() $< p _ { m }$ then 12 child child.mutate() 13 else 14 child parent1.mutate() 15 child.evaluate() 16 interP op.add(child) 17 pop ← merge(pop, interP op) 18 return pop.getBestSolutions(n)

of the algorithm is shown in Algorithm 1. It starts with random initialization and evaluation of the population of solutions, pop. Specifically, a population of solutions is generated so that each solution is assigned a randomly generated priority list as a random permutation of all parts, which is then mapped into the corresponding production assignment and its performance parameters sat and dist are calculated. The algorithm then iterates through a specified number of generations, lines 3–17. In each generation, an intermediate population of solutions, interP op, is created using the solutions of the current population $p o p$ , lines 6–16. First, parental solutions are selected, which then undergo the crossover and mutation operations to create their offspring solution child. Each newly created child solution is evaluated. Once the interP op has been completed, it is merged with the current pop, resulting in a new version of the pop (line 17). While merging the two populations, only unique solutions are kept. The better solutions are preferred and ties are broken in favour of the new interP op solutions. Finally, $n$ best solutions of the final population are returned as the output of the run.

# 3.3. Phase II

Upon completion of phase I and generating the production assignment, the subsequent phase II resides in completing the industrial system with transportation links between industrial system components, with the quantity of required parts production based on site shares, with information about production duration, and with transportation details — duration, costs, $C O _ { 2 }$ emission, and how many batches are required to satisfy demand in target sites.

Thanks to EA multicriteria optimization, which considers not only the solution’s satisfiability but also minimize the transportation distance, the space of all possible solutions is significantly pruned. Thus, the task of this phase represents forming the graph of sites and possible connections between sites (together with detailed transportation information), supplemented by warehouses. The output of the first phase includes not only information about producers and consumers, but also information about production shares. Therefore, this task may be solved by leveraging local optimization propagating through the graph against production time.

# 3.3.1. Time Matters

To bring the solution closer to reality, it is important to take into account the time variable. Compared with other approaches, for example, simulations in which time may be treated in a continuous (or discrete) infinite manner, the proposed approach in this solution restricts the duration of the overall process to an interval according to the amount demanded of the final product (i.e., the number of “Single Aisle Aircraft”).

The idea of how to cope with the time issue is as follows. First, the time interval is computed for the final assembly and, as the next step(s), it is spread through the industrial system to the lowest levels where fundamental parts are produced. Batching is taken into account in this step to find out how many turns/deliveries are required for every transportation link. Dependencies on more various input parts for many production sites may lead to delays which are covered by this approach.

Furthermore, the solution more precisely approximates the real-life situation in terms of time and logistics with an increasing number of final products.

# 3.3.2. Transportation Network Generation

First, a transportation network has to be generated. The generation process is based on input from the first phase, that is, the already defined architecture of the industrial system in the form of the production assignment. Thus, the backbone of the producer/consumer network is available and has to be extended by the following components and properties:

• Edges between nodes, where nodes represent production sites (i.e., producers and consumers). Every edge represents a specific type of transportation with relevant properties — the number of given products in one container, the number of containers required for demand satisfaction, transportation time, distance, transportation cost, and $C O _ { 2 }$ emissions.

• Warehouse nodes and their connections. The involvement of warehouses in transportation network represents a complex task. This approach relies on flows between producers and consumers already defined by the evolutionary algorithm (from the first phase), and, therefore, the task is to decide if the material is transported directly or routed via one or more intervening warehouses (for example, source or/and target warehouses where source warehouse is located nearby producers and target warehouse is located nearby consumer/- consumers). The “nearby” property of warehouses refers to “isNearby” OWL object property from the source ontology. A mixed batching strategy is taken into account for links between warehouses in the case where the various parts are available in the warehouse and are required to be transported together at the same time.

The outcome of the transportation network generation task is the directed acyclic graph with possibly redundant edges between two nodes in the case of more possi

![](images/38388aed15a2d5b37a554a0928a626cd814b1fde45cf0a2d2156f9621d0c8bcd.jpg)

Fig. 2. Part of the transport network with transport links, distances according to transportation means, and two warehouses. In the depicted oriented graph, vertices represent production units and edges between vertices represent transport links between production units. There is typically more than one edge between two vertices, where every edge stands for a different transportation resource. There are two warehouses named “City 2 Harbor” and “City 4 Harbor”.

ble transportation means. The part of the transport network with transport links together with distances by possible transportation means is shown in Fig 2. The last thing to be specified in this step is the specification of the required amount of parts and their type/s. Together with the related computation of the batching task (described in the following section), it is done in a top-down manner.

# 3.3.3. Batching Strategies

Batching solves the important task of how to load parts into a cargo container/mean. The inputs are the type of part (resp. parts) to be delivered, its (resp. their) dimensions, and the required amount. Afterwards, the output is information on how many parts fit into the container or transportation means and how many deliveries are required to fulfil the given demand.

• Single-batching strategy — items to pack are of one particular type. Primarily, it represents a batching for transportation for direct transportation links, that is, directly between a source and a target manufacturing resource (site). • Mixed-batching strategy — items to pack are represented by various part types. It represents a batching between two warehouses, where the items may be mixed.

Two bin-packing algorithms were tested — Largest Area Fit First algorithm (G¨urb¨uz et al., 2009) and the GRASP heuristics-based algorithm (swap and relocate neighbourhood search) (Layeb and Chenche, 2012). The precise shape of parts is not considered, and every part is represented by its bounding box. For this application, both of the aforementioned algorithms have comparable results. The GRASP algorithm was selected for use in this solution. The sample output of the batching task is presented in the following listings.

Listing 1. An output from the batching algorithm describing bounding box size of the part, size of the container, demanded amount of the part, number of containers (resp. number of deliveries), part type, and type of the transportation resource.

p a r t s i z e : [ 1 2 3 0 0 , 2300 , 1800]   
c o n t a i n e r s i z e : [ 1 4 8 0 0 , 3300 , 3000]   
demand : 3   
nb o f c o n t a i n e r s : 3   
p a r t : H o r i z o n t a l T a i l p l a n e   
t r a n s p o r t a t i o n : Truck Oversized Low Bed   
p a r t s i z e : [ 6 8 0 0 , 400 , 1500]   
c o n t a i n e r s i z e : [ 2 3 3 0 , 11998 , 2350]   
demand : 8   
nb o f c o n t a i n e r s : 2   
p a r t : Pylon Right

With the help of batching, we have information about not only how many parts fit into a given cargo capacity but also how invalid transport links may be pruned. In other words, if the part cannot be loaded into a container, then the transport link between the source site and the target site using a certain transportation means is considered invalid and is not taken into consideration for further processing.

In the case of a shared part of the transport link between the source production unit, one or more warehouses, and the target production unit, the mixed batching is applied for the shared and continuous path. Finally, information about batches (single or mixed) is stored in the transportation network graph to the relevant transport links.

# 3.3.4. Transport Optimization

The pruned space of possible solutions from the result of the phase Iis used for the final optimization. As mentioned in previous paragraphs, possible solutions form the graph, which contains information about $C O _ { 2 }$ , costs and duration within the supply chain, distance, batch size, and batch count with respect to the available transportation means.

Based on the nature of the task and its limited scale/size after the preceding processing, local optimization propagating through the graph against production time was proposed as sufficient and suitable. The Descent Recursive Acyclic Graph Optimizer (DRAGO) algorithm aims to optimize transportation between production units and warehouses. The pseudo-code is presented in Algorithm 2. Given a starting vertex (i.e., final assembly line — line 2 in Algorithm 2) and an optimization criterion (e.g., duration, cost, emissions), DRAGO recursively explores the graph in a topdown manner. This is achieved by the OptimizeInput function (line 10), which, for a given vertex and input part, evaluates all relevant incoming transportation links based on the defined optimization criteria. The relevant incoming transportation links are selected by AdjacentEdges function (line 14), which finds input links to vertex transporting specified input product. Next, the link with the best score is selected. If the selected transportation link involves a mixed batch (indicated by transport link.parts.size $> 1$ — line 19), the current vertex-input pair is added to a mixed batching inputs list (line 24) for subsequent joint processing. Otherwise, the selected best transportation link is added to the optimal path (line 26). The algorithm then recursively calls OptimizeInput for the source vertex of the chosen transportation link and each of its required input parts (line 29), effectively propagating the optimization down the supply chain.

# 4. Related work

In this section, we provide an overview of recent works dealing with the design of distributed manufacturing and the related optimization of the transportation network. Note that our approach addresses all these aspects jointly, including additional factors such as batching and the use of distributed buffer warehouses.

Research on multi-site production planning has addressed how to assign the manufacturing of various parts or products to a network of available plants. For example, Shen et al. (Shen et al., 2020) develop a multi-plant production planning model that incorporates practical features like non-repeated setup operations and aperiodic shipment schedules. They formulate the problem as a mixed-integer linear program (MILP) with additional linearized constraints, solving it to optimality to maximize total profit under these complex setup and shipment conditions. In a similar vein, ¸C¨omez-Dolgan et al. (C¸ ¨omez Dolgan et al., 2023) consider a manufacturer with multiple plants facing regional demands for a wide product assortment. Their work focuses on deciding which products to produce at each plant (assortment planning) when trans-shipments between plants are allowed at an extra cost. The authors propose an optimization model to maximize profit, and derive structural properties (such as nested optimal assortments) of the solution. Han et al. (Han et al., 2019) address a two-level supply chain where multiple suppliers produce semi-finished parts that are

# Algorithm 2: DRAGO

1 REQUIRE Acyclic graph $G ( V , E )$ , where $V$ are production units or warehouses and $E$ are   
transportation links with properties (transportation mean, distance, $C O _ { 2 }$   
emissions, duration, size of batch, number of drives, transportation costs)   
2 REQUIRE Starting vertex $v _ { s t a r t }$ (final assembly line)   
3 REQUIRE Optimization criteria (e.g., duration, distance, $C O _ { 2 }$ emissions,   
transportation costs)   
4 optimal path $ [ ]$   
5 mixed batching inputs ← []   
6 Function Drago():   
7 for each input par $t \in v _ { s t a r t }$ do   
8 OptimizeInput $\left( v _ { s t a r t } \right.$ , input part)   
9 return optimal path   
10 Function OptimizeInput(vertex, input):   
11 best transportation link $$ NULL   
12 best score $ \infty$   
13 mixed b $\mathit { a t c h } \gets \mathrm { F A L } _ { \mathrm { ~ } } ^ { \mathrm { ~ } }$ SE   
14 for each transportation link $\in$ AdjacentEdges(vertex, input) do   
15 Calculate score based on optimization criteria   
16 if score $<$ best score then   
17 best transportation link $$ transportation link   
18 best score score   
19 if transport link.parts.size $> 1$ then   
20 mixed batch $$ TRUE   
21 break   
22 if best transportation li $n k \neq N U L L$ then   
23 if mixed batch then   
24 Append (vertex, input) to mixed batching inputs   
25 else   
26 Append best transportation link to optimal path   
27 current vertex $$ source vertex of best transportation link   
28 for each input part $\in$ current vertex do   
29 OptimizeInput(current vertex, input part)   
30 return

sent to a central assembly plant. They combine a cost-minimization MILP to select suppliers and production quantities each period with a tailored heuristic algorithm. Similarly, Wibawa et al. (Wibawa et al., 2022) explore metaheuristic approaches for multi-site aggregate production planning. They consider a textile manufacturer with several production sites and formulate a multi-objective aggregate planning problem to minimize costs. They employ a particle swarm optimization (PSO) algorithm to search for near-optimal production allocation plans. Their PSO-based method demonstrated in a real case study that it can effectively handle large-scale multifactory allocation problems.

Another stream of related work focuses on optimizing transportation and distribution networks in global supply chains. These studies typically assume production locations are given and seek to minimize logistics costs by better routing, scheduling, and network design. Guo et al. (Guo et al., 2022) investigate a spare-parts distribution network that must supply manufacturing plants over multiple periods. They present a dynamic nonlinear programming model that jointly manages inventory at customer facilities and flow decisions in the spare-parts network. To solve this complex problem, the authors develop an improved self-adaptive dynamic PSO algorithm that quickly adapts to changing conditions. Peng et al. (Peng et al., 2022) study an integrated transportation planning problem in a retail supply network under carbon emission regulations and demand uncertainty. They use uncertainty theory to model ambiguous data (e.g., demand fluctuations) and formulate four mathematical programming models corresponding to different carbon regulatory policies. In the domain of inbound logistics, Quan et al. (Quan et al., 2021) focus on optimizing milk-run routes for delivering auto parts from multiple suppliers to an assembly plant under low-carbon objectives. They extend the classic vehicle routing problem by adding fixed delivery schedules (time windows), fuel consumption, and carbon emission costs into the routing model. An improved ant colony optimization algorithm is then applied to find efficient pickup routes. Transportation network optimization can also be integrated with inventory decisions. Vicente (Vicente, 2025) proposes a MILP-based planning tool that coordinates inventory control and shipment scheduling across a multi-echelon distribution system. The model determines optimal replenishment quantities and shipping plans for warehouses and retailers, following a periodic-review inventory policy. Experiments with realistic supply chain data show that jointly optimizing inventory and transportation (rather than treating them separately) can significantly lower overall supply chain costs.

Integrated approaches consider production allocation and transportation planning decisions simultaneously and capture trade-offs between manufacturing and logistics. Klenk et al. (Klenk et al., 2022) present a comprehensive model for global production network configuration, which jointly optimizes product allocations to plants and the distribution network over a planning horizon. Their approach is multi-objective: it minimizes cost and other metrics while incorporating flexibility and reconfiguration considerations. Using a preemptive goal programming method, they generate Pareto-optimal solutions that balance conflicting objectives. Multi-objective mathematical programming is a common tool in integrated production-distribution planning. Badhotiya et al. (Badhotiya et al., 2019) formulate a fuzzy multi-objective MILP for a two-echelon supply chain with multiple plants and distribution centers. Their model optimizes production quantities at each plant and product flows to distribution centers, aiming to maximize profit and service level while minimizing costs. They introduce fuzzy goal programming to handle the trade-offs among objectives and uncertainties in demand. The use of fuzzy objectives provides decision-makers flexibility in prioritizing goals in an integrated plan. Another notable contribution is by Neiro et al. (Neiro et al., 2022), who tackle a production-distribution coordination problem in the industrial gas industry. They consider a network of production plants producing liquid argon and a set of customers served by tanker trucks. The challenge is to balance production levels at each site with vehicle routes and delivery schedules to meet customer demand at minimum cost. The authors adopt a two-phase solution: first generate efficient delivery routes via a routing heuristic, and then include those routes in a unified MILP that optimizes both production schedules and distribution assignments. This decomposition approach yields high-quality integrated plans for a real-world scale problem. Recent studies have started to address integrated production–distribution planning with explicit sustainability and uncertainty considerations in industrial contexts. For example, Xue et al. (Xue et al., 2023) develop a multi-objective model for joint production-distribution optimization in an automobile supply chain under sustainability constraints. Their approach considers a hyper-connected, Physical Internet-enabled order-to-delivery system, optimizing both operational cost and environmental performance measures. By applying an improved NSGA-III algorithm, this work demonstrates that substantial gains in efficiency and eco-performance can be achieved simultaneously in a realistic automotive scenario.

# 5. Experiments

In this section, we present proof-of-concept experiments to demonstrate the effectiveness of the proposed approach. First, phase I experiments on two problem types, single and double sourcing, are presented. Then, phase II experiments with the following five variants of the constraint optimization problem are described:

1. Find the industrial system $I ( E , N )$ such that the objective $f _ { 1 } ( \cdot )$ is minimized.   
2. Find the industrial system $I ( E , N )$ such that the objective $f _ { 2 } ( \cdot )$ is minimized.   
3. Find the industrial system $I ( E , N )$ such that the objective $f _ { 3 } ( \cdot )$ is minimized.   
4. Find the industrial system $I ( E , N )$ such that the objective $f _ { 4 } ( \cdot )$ is minimized.   
5. Find the industrial system $I ( E , N )$ such that both objectives $f _ { 1 } ( \cdot )$ and $f _ { 2 } ( \cdot )$ are   
minimized simultaneously.

The first four variants are single-objective optimization problems, while the fifth one is a bi-objective optimization problem. In all five cases, the optimization task faces manifold challenges. First, a valid and well-performing production assignment must be generated in phase I. Then, given the production assignment found, finding the optimal transportation graph in phase II requires choosing its optimal topology (i.e., choosing between direct part transportation or routing via warehouse(s)) and optimally choosing the attributes of all transportation links, the type of transport and the size of the batch.

# 5.1. Experiment setup

To test our approach, we used a case study from the aerospace manufacturing sector with the following parameters: $| P | = M = 4 7$ , $| C | = 1 7$ , $| S | = 2 9$ , $| L | = 4 3$ , $| U | = 4 5$ , $| W | = 3 4$ , $| T | = 1 7$ (9 types of maritime transport, 6 types of freight road transport, 2 types of air transport). Each transportation type has its CO2 emission, speed, and maximum load volume defined. The upper part of Figure 3 shows the respective production graph. The required parameters of the industrial system sought are:

• The maximum value added by all countries but France, USA and the United Kingdom is set to $v _ { m a x } ^ { c } = 0 . 1$ , see Table 4. France and the United Kingdom used $v _ { m a x } ^ { c }$ set to 0.22 and 0.12, respectively, due to the single-country parts produced only in these two countries.

The USA used $v _ { m a x } ^ { c }$ set to 0.2 since it is one of the two producers (the other one is France) of “engine right” and “engine left”, where both products have the value added of 0.1. Also, the USA has two final assembly line (FAL) production units, producing the “Single Aisle Aircraft” whose value added is 0.11. Thus, setting the $v _ { m a x } ^ { c }$ to the value 0.2 makes it possible to reasonably utilize the USA production units.

• The maximum value added by a supplier is the same $v _ { m a x } ^ { s } = 0 . 1 5$ for all suppliers. • The maximum value added by a production unit is set to $v _ { m a x } ^ { u } = 0 . 1 5$ for all production units.

# 5.2. Phase I - Experiments

This section presents the empirical evaluation of the EA. The EA was run with the following configuration: population size 500, number of generations $G = 2 0 0$ , tournament size 3, crossover rate $p _ { c } = 0 . 8$ , mutation rate $p _ { m } = 0 . 1$ .

# 5.2.1. Results

Table 1 and Table 2 show results obtained for the “single sourcing” and “double sourcing” scenario, respectively. The single sourcing scenario considers a single production unit assigned to all parts but the Single Aisle Aircraft $^ { 1 }$ , for which two production units are required. The double sourcing scenario requires that two production units produce every part while satisfying Constraints 2 and 3. In both cases, the initial population of the EA consists exclusively of invalid solutions, i.e., the solutions with sat values less than 1. This is not surprising as the solutions are randomly generated. The optimization process concludes with solutions superior to the randomly generated ones in both the sat and $d i s t _ { R }$ . One can also see that the solutions for the double sourcing scenario have much bigger $d i s t _ { R }$ values than the single sourcing solutions. This is also consistent with our expectations, since the double sourcing scenario inherently requires higher transportation demands. Details of one of the solutions obtained for the double sourcing are presented in Figure 3, Listing 2, Table 3, and Table 4.

# 5.3. Phase II - Experiments

The phase II serves for the final optimization of the industrial system regarding the flows between production sites, aggregating parts in warehouses, and the selection of best-fitting transportation means based on the batching and according to the specified criteria for the optimization.

Table 1. Results of five independent runs of the evolutionary algorithm for the single sourcing scenario   

<html><body><table><tr><td>run</td><td>mean initial sat</td><td>best initial distr [km]</td><td>best final distr [km]</td></tr><tr><td>1</td><td>0.75</td><td>361531</td><td>314825</td></tr><tr><td>2</td><td>0.82</td><td>358380</td><td>203101</td></tr><tr><td>3</td><td>0.86</td><td>297410</td><td>248772</td></tr><tr><td>4</td><td>0.84</td><td>264009</td><td>210220</td></tr><tr><td>5</td><td>0.79</td><td>273381</td><td>214430</td></tr></table></body></html>

Table 2. Results of five independent runs of the evolutionary algorithm for the double sourcing scenario   

<html><body><table><tr><td>run</td><td>mean initial sat</td><td>best initial distr [km]</td></tr><tr><td>1 0.92</td><td></td><td>799073 456173</td></tr><tr><td>2</td><td>0.87</td><td>706505 460592</td></tr><tr><td>3</td><td>0.89</td><td>705729 425626</td></tr><tr><td>4</td><td>0.86</td><td>660926 469043</td></tr><tr><td>5</td><td>0.91</td><td>632954 471073</td></tr></table></body></html>

The bottom part of Fig 3 presents a portion of the transportation graph that was generated based on the outcome from phase I. It shows possible connections between production sites and warehouses along with information about transportation means. Information about properties such as the number of parts (i.e., the batch size) to transport, the number of trips with given batches, distance, and duration are not depicted due to the clarity of the figure.

This graph is the input for the optimization using DRAGO algorithm. The results of phase II are shown in Table 5. It summarizes results calculated using one particular output from phase I and presents KPIs for $C O _ { 2 }$ , duration, distance, and transportation costs optimization. Furthermore, the third and the fourth rows provide duration/ $C O _ { 2 }$ trade-off optimization. The number of produced aircraft was 40 to employ and test batching in the optimization. The comparison of $C O _ { 2 }$ , duration, distance, and transportation costs optimizations with respect to overall transportation distance (resp. duration, $C O _ { 2 }$ , and transportation costs) divided according to transportation means is provided in Table 6 (resp. in Table 7, Table 8, and Table 9).

Table 3. Distribution of the added value (AV) among suppliers and their production sites.   

<html><body><table><tr><td>Supplier</td><td>Country</td><td>Site</td><td>AV</td><td>Supplier</td><td>Country</td><td>Site</td><td>AV</td></tr><tr><td>airbus operations Germany</td><td>Germany</td><td>l4</td><td>0.003</td><td>generic buy 7</td><td>France</td><td>13</td><td>0.029</td></tr><tr><td>airbus operations Germany</td><td>Germany</td><td>l31</td><td>0.040</td><td>generic buy 7</td><td>France</td><td>l37</td><td>0.012</td></tr><tr><td>airbus operations France</td><td>France</td><td>38</td><td>0.020</td><td>generic buy 7</td><td>Africa-c2</td><td>l39</td><td>0.015</td></tr><tr><td>airbus operations France</td><td>France</td><td></td><td>0.005</td><td>generic buy 7</td><td>Europe-c8</td><td>l40</td><td>0.003</td></tr><tr><td>airbus operations UK</td><td>United Kingdom</td><td>19</td><td>0.004</td><td>generic buy 7</td><td>France</td><td>136</td><td>0.004</td></tr><tr><td>airbus operations UK</td><td>United Kingdom</td><td>18</td><td>0.050</td><td>generic buy8</td><td>France</td><td>l41</td><td>0.052</td></tr><tr><td>airbus operations Spain</td><td>Spain</td><td>1</td><td>0.019</td><td>generic buy 9</td><td>Asia-c1</td><td>42</td><td>0.007</td></tr><tr><td>airbus operations Spain</td><td>Spain</td><td>l6</td><td>0.012</td><td>generic buy 10</td><td>Germany</td><td>112</td><td>0.003</td></tr><tr><td>airbus operations Spain</td><td>Spain</td><td>l5</td><td>0.000</td><td>generic buy 11</td><td>France</td><td>13</td><td>0.002</td></tr><tr><td>airbus operations NA2</td><td>NorthAmerica-c2</td><td>l10</td><td>0.011</td><td>generic buy 12</td><td>Europe-c5</td><td></td><td>0.045</td></tr><tr><td>airbus operations NA1</td><td>NorthAmerica-c1</td><td>l1</td><td>0.000</td><td>generic buy 13</td><td>EastAsia-c1</td><td>15</td><td>0.039</td></tr><tr><td>airbus operations Asia-c1</td><td>Asia-c1</td><td></td><td>0.039</td><td>generic buy 14</td><td>Europe-c6</td><td>16</td><td>0.025</td></tr><tr><td>generic buy 1</td><td>Spain</td><td>l11</td><td>0.005</td><td>generic buy 15</td><td>Germany</td><td>117</td><td>0.004</td></tr><tr><td>generic buy 2</td><td>Europe-c7</td><td>123</td><td>0.080</td><td>generic buy 16</td><td>Germany</td><td>118</td><td>0.013</td></tr><tr><td>generic buy 3</td><td>Germany</td><td>l28</td><td>0.011</td><td>generic buy 17</td><td>NorthAmerica-c2</td><td>119</td><td>0.148</td></tr><tr><td>generic buy 4</td><td>Africa-c1</td><td>l29</td><td>0.030</td><td>generic buy 18</td><td>Germany</td><td>20</td><td>0.004</td></tr><tr><td>generic buy 5</td><td>EastAsia-c2</td><td>l30</td><td>0.008</td><td>generic buy 19</td><td>France</td><td>l21</td><td>0.006</td></tr><tr><td>generic buy 6</td><td>Germany</td><td>134</td><td>0.000</td><td>generic buy 19</td><td>United Kingdom</td><td>122</td><td>0.003</td></tr><tr><td>generic buy 6</td><td>Europe-c9</td><td>l35</td><td>0.038</td><td>generic buy 20</td><td>United Kingdom</td><td>125</td><td>0.050</td></tr><tr><td>generic buy 6</td><td>Germany</td><td>132</td><td>0.015</td><td>generic buy 20</td><td>France</td><td>l24</td><td>0.003</td></tr><tr><td>generic buy 6</td><td>Germany</td><td>l31</td><td>0.007</td><td>generic buy 21</td><td>Europe-c10</td><td>126</td><td>0.037</td></tr><tr><td>generic buy 7</td><td>France</td><td>l24</td><td>0.014</td><td>generic buy 22</td><td>Asia-c1</td><td>l27</td><td>0.012</td></tr><tr><td>generic buy 7</td><td>France</td><td>l38</td><td>0.071</td><td></td><td></td><td></td><td></td></tr></table></body></html>

total: 1.0   
Table 4. Distribution of the added value among countries.   

<html><body><table><tr><td>Country</td><td>AV(min, max)</td><td>actual AV</td></tr><tr><td>France</td><td>(0.05,0.22)</td><td>0.218</td></tr><tr><td>Germany</td><td>(0.05,0.1)</td><td>0.1</td></tr><tr><td>Spain</td><td>(0.05, 0.1)</td><td>0.037</td></tr><tr><td>United Kingdom</td><td>(0.05,0.12)</td><td>0.107</td></tr><tr><td>Europe-c5</td><td>(0.05, 0.1)</td><td>0.045</td></tr><tr><td>Europe-c6</td><td>(0.05, 0.1)</td><td>0.025</td></tr><tr><td>Europe-c7</td><td>(0.05,0.1)</td><td>0.08</td></tr><tr><td>Europe-c8</td><td>(0.05,0.1)</td><td>0.003</td></tr><tr><td>Europe-c9</td><td>(0.05, 0.1)</td><td>0.038</td></tr><tr><td>Europe-c10</td><td>(0.05, 0.1)</td><td>0.037</td></tr><tr><td>NorthAmerica-c1</td><td>(0.05, 0.1)</td><td>0.0</td></tr><tr><td>NorthAmerica-c2</td><td>(0.05,0.2)</td><td>0.159</td></tr><tr><td>Asia-c1</td><td>(0.05, 0.1)</td><td>0.059</td></tr><tr><td>EastAsia-c1</td><td>(0.05, 0.1)</td><td>0.039</td></tr><tr><td>EastAsia-c2</td><td>(0.05,0.1)</td><td>0.008</td></tr><tr><td>Africa-c1</td><td>(0.05,0.1)</td><td>0.03</td></tr><tr><td>Africa-c2</td><td>(0.05, 0.1)</td><td>0.015</td></tr></table></body></html>

Table 5. phase II results: Comparison of $C O _ { 2 }$ , duration, distance, and transportation costs optimization with respect to all KPIs (duration, distance, $C O _ { 2 }$ , transportation costs). The number of final products was set to 40 aircraft.   

<html><body><table><tr><td></td><td>Duration [h]</td><td>Distance [km]</td><td>CO2 emissions [g]</td><td>Transportation costs [EUR/km]</td></tr><tr><td>CO2 optimization</td><td>62,490</td><td>1,773,169</td><td>3.308E+09</td><td>7,909,327</td></tr><tr><td>Duration optimization</td><td>35,199</td><td>1,309,184</td><td>11.394E+09</td><td>13,551,441</td></tr><tr><td>Duration/CO2 trade-off:1</td><td>40,315</td><td>1,384,028</td><td>3.483E+09</td><td>7,846,041</td></tr><tr><td>Duration/CO2 trade-off:2</td><td>36,788</td><td>1,317,799</td><td>7.348E+09</td><td>9,884,600</td></tr><tr><td>Distance optimization</td><td>37,202</td><td>1,280,896</td><td>10,572E+09</td><td>14,382,365</td></tr><tr><td>Transportation costs optimization</td><td>52,076</td><td>1,584,059</td><td>3.373E+09</td><td>7,287,918</td></tr></table></body></html>

The outcomes of this phase were verified using previously developed simulation model (Schirrmann, 2022).

# 6. Conclusions and Further Steps

This paper introduces a novel approach to optimizing global industrial systems focusing on the aerospace manufacturing sector. Through a two-phase method, it addresses the challenges of designing an efficient industrial system architecture and optimizing the associated transportation network. The integration of evolutionary algorithms and formal optimization techniques demonstrates the ability to handle complex constraints and generate practical solutions.

The proposed framework effectively combines a data-driven model with optimization strategies and it enables the exploration of possible production and logistics

Table 6. phase II results: Transportation distances (km) achieved using different optimization strategies (all of which are minimization-based) – $C O _ { 2 }$ emissions, duration, distance, and transportation costs. Each column represents the distribution of the total distance obtained with the given strategy among the six types of transportation means: large container ship, specialized transport, truck, oversized RoRo ship, oversized Beluga XL, and Kugelbake. The number of final products was set to 40 aircraft.

<html><body><table><tr><td rowspan="2"></td><td colspan="3">Distance [km]</td></tr><tr><td>Duration CO2 opt. opt.</td><td>Distance opt.</td><td>Transp. costs opt.</td></tr><tr><td>Large Container Ship</td><td>1,020,654.68</td><td>76,062.67 272,914.46</td><td>831,116.11</td></tr><tr><td>Specialized Transp.</td><td>3,145.14 3,587.38</td><td>11,472.92</td><td>3,145.14</td></tr><tr><td>Truck</td><td>304,904,85 419,861.12</td><td>335,622.15</td><td>438,559</td></tr><tr><td>Oversized RoRo Ship</td><td>385,860.09 588,882.36</td><td>457,986.21</td><td>252,634.47</td></tr><tr><td>Oversized Beluga XL</td><td>58,605.07</td><td>220,790.60 202,599.99</td><td>58,605.07</td></tr><tr><td>Kugelbake</td><td>0 0</td><td>300.9</td><td>0</td></tr></table></body></html>

configurations. The backbone is represented by OWL ontology, which ensures consistency checks mainly in matching between a part to be produced and available transportation resources. The first phase ensures the creation of a valid production assignment that complies with predefined constraints. The second phase focuses on refining transportation networks and batching strategies to minimize the overall transportation time and the total amount of CO $_ 2$ generated.

The experimental results validate the approach’s feasibility and are demonstrated by several tested scenarios. The presented approach has been shown to provide a solid foundation for developing resilient and sustainable global industrial systems with potential applications not limited only to aerospace manufacturing.

Future work will enhance the robustness of the approach, and several future steps are proposed:

Table 7. phase II results: Transportation duration (h) for different optimization strategies (all of which are minimization-based) – $C O _ { 2 }$ emissions, duration, distance, and transportation costs. Each column represents the distribution of the total transportation duration obtained with the given strategy among the six types of transportation means: large container ship, specialized transport, truck, oversized RoRo ship, oversized Beluga XL, and Kugelbake. The number of final products was set to 40 aircraft.

<html><body><table><tr><td rowspan="2"></td><td colspan="3">Duration [h]</td></tr><tr><td>Duration CO2 opt. opt.</td><td>Distance opt.</td><td>Transp. costs opt.</td></tr><tr><td>Large Container Ship</td><td>40,826.18 3,042.50</td><td>10,916.57</td><td>33,244.64</td></tr><tr><td>Specialized Transp.</td><td>314.51 358.73</td><td>1,147.29</td><td>314.51</td></tr><tr><td>Truck</td><td>5,822 7,889.47</td><td>6,470.92</td><td>8,317.88</td></tr><tr><td>Oversized RoRo Ship</td><td>15,434.40 23,555.29</td><td>18,319.44</td><td>10,105.37</td></tr><tr><td>Oversized Beluga XL</td><td>93.77 353.26</td><td>324.15</td><td>93.77</td></tr><tr><td>Kugelbake</td><td>0 0</td><td>24.07</td><td>0</td></tr></table></body></html>

• Explore simulation-based techniques to complement optimization, particularly for tasks like mixed batching and warehouse utilization.

• Integrate advanced optimization techniques, such as multi-objective evolutionary algorithms, to simultaneously address multiple conflicting criteria, including cost, CO2 emissions, and transportation time in the phase I.

• The algorithm implemented and used in phase II is sufficient for the problems of this scale. However, an exploitation of algorithms for example from a Linear Optimization (LP) (Bertsimas and Tsitsiklis, 1997) such as the Network Simplex Method (Cunningham, 1976) is planned for following experiments.

Table 8. phase II results: $C O _ { 2 }$ emissions (g) for different optimization strategies (all of which are minimization-based) – $C O _ { 2 }$ emissions, duration, distance, and transportation costs. Each column represents the distribution of the total $C O _ { 2 }$ emissions obtained with the given strategy among the six types of transportation means: large container ship, specialized transport, truck, oversized RoRo ship, oversized Beluga XL, and Kugelbake. The number of final products was set to 40 aircraft.

<html><body><table><tr><td rowspan="2"></td><td colspan="4">CO2 emissions [g]</td></tr><tr><td>CO2 opt.</td><td>Duration opt.</td><td>Distance opt.</td><td>Transp. costs opt.</td></tr><tr><td>Large Container Ship</td><td>6.369E+07</td><td>4,746E+06</td><td>1.703E+07</td><td>5.186E+07</td></tr><tr><td>Specialized Transp.</td><td>5.661E+07</td><td>6.457E+07</td><td>2.065E+08</td><td>5.661E+07</td></tr><tr><td>Truck</td><td>22.733E+07</td><td>30.833E+07</td><td>2.526E+08</td><td>32,499E+07</td></tr><tr><td>Oversized RoRo Ship</td><td>6.174E+07</td><td>9.422E+07</td><td>7.330E+07</td><td>4.042E+07</td></tr><tr><td>Oversized Beluga XL</td><td>2.899E+09</td><td>1.092E+10</td><td>1.002E+10</td><td>2.899E+09</td></tr><tr><td>Kugelbake</td><td>0</td><td>0</td><td>248,724</td><td>0</td></tr></table></body></html>

# References

Aickelin, U., Clark, A., 2011. Heuristic optimisation.

Airbus, 2021. Airbus A380 Facts and Figures. Technical Report. Airbus. URL: https://www.airbus.com/sites/g/files/jlcbta136/files/ 2021-12/EN-Airbus-A380-Facts-and-Figures-December-2021_0.pdf.

Antoniou, G., Harmelen, F.v., 2009. Web ontology language: Owl. Handbook on ontologies , 91–110.

Badhotiya, G., Soni, G., Mittal, M., 2019. Fuzzy multi-objective optimization for multi-site integrated production and distribution planning in two echelon supply chain. The International Journal of Advanced Manufacturing Technology 102. doi:10.1007/s00170-018-3204-2.

Table 9. phase II results: Transportation costs (EUR/km) for different optimization strategies (all of which are minimization-based) – $C O _ { 2 }$ emissions, duration, distance, and transportation costs. Each column represents the distribution of the total transportation costs obtained with the given strategy among the six types of transportation means: large container ship, specialized transport, truck, oversized RoRo ship, oversized Beluga XL, and Kugelbake. The number of final products was set to 40 aircraft.

<html><body><table><tr><td rowspan="2"></td><td colspan="3">Transportation costs [EUR/km]</td></tr><tr><td>Duration CO2 opt. opt.</td><td>Distance opt.</td><td>Transp. costs opt.</td></tr><tr><td>Large Container Ship</td><td>1,469,742 109,530</td><td>392,996</td><td>1,196,807</td></tr><tr><td>Specialized Transp.</td><td>874,348 997,291</td><td>3,189,471</td><td>874,348</td></tr><tr><td>Truck</td><td>1,220,366 1,610,470</td><td>1,377,567</td><td>1,724,537</td></tr><tr><td>Oversized RoRo Ship</td><td>2,469,504 3,768,847</td><td>2,932,089</td><td>1,616,860</td></tr><tr><td>Oversized Beluga XL</td><td>1,875,362 7,065,299</td><td>6,483,199</td><td>1,875,362</td></tr><tr><td>Kugelbake</td><td>0 0</td><td>3,944</td><td>0</td></tr></table></body></html>

Bertsimas, D., Tsitsiklis, J.N., 1997. Introduction to linear optimization. volume 6. Athena Scientific Belmont, MA.

Chai, Z.Y., ying Nie, Li, Y.L., 2025. A multi-objective multi-task evolutionary algorithm based on source task transfer. Applied Soft Computing , 112732URL: https://www.sciencedirect.com/science/article/pii/ S1568494625000432, doi:https://doi.org/10.1016/j.asoc.2025.112732.   
Cunningham, W.H., 1976. A network simplex method. Mathematical Programming 11, 105–116.   
De Giacomo, G., Lenzerini, M., et al., 1996. Tbox and abox reasoning in expressive description logics. KR 96, 10.   
Dietz, E., Philipp, T., Schramm, G., Zindel, A., 2023. A logic programming approach

to global logistics in a co-design environment. Electronic Proceedings in Theoretical Computer Science 385, 227–240. URL: http://dx.doi.org/10.4204/EPTCS.385. 23, doi:10.4204/eptcs.385.23.

¸C¨omez Dolgan, N., Da˘g, H., Fescioglu-Unver, N., ¸Sen, A., 2023. Multi-plant manufacturing assortment planning in the presence of transshipments. European Journal of Operational Research 310, 1033–1050. URL: https://www.sciencedirect.com/ science/article/pii/S0377221723002333, doi:https://doi.org/10.1016/j. ejor.2023.03.026.   
Drummond, N., Shearer, R., 2006. The open world assumption, in: eSI Workshop: The Closed World of Databases meets the Open World of the Semantic Web, p. 1.   
Guo, Y., Shi, Q., Guo, C., 2022. Multi-period spare parts supply chain network optimization under (t, s, s) inventory control policy with improved dynamic particle swarm optimization. Electronics 11, 3454. doi:10.3390/electronics11213454.   
G¨urb¨uz, M.Z., Akyokus¸, S., Emiro˘glu, ˙I., G¨uran, A., 2009. An efficient algorithm for 3d rectangular box packing. Applied Automatic Systems: Proceedings of Selected AAS 2009 Papers .   
Han, J.H., Lee, J.Y., Kim, Y.D., 2019. Production planning in a two-level supply chain for production-time-dependent products with dynamic demands. Computers & Industrial Engineering 135, 1–9. URL: https://www.sciencedirect.com/ science/article/pii/S0360835219303092, doi:https://doi.org/10.1016/j. cie.2019.05.036.   
Horrocks, I., Patel-Schneider, P.F., Boley, H., Tabet, S., Grosof, B., Dean, M., et al., 2004. Swrl: A semantic web rule language combining owl and ruleml. W3C Member submission 21, 1–31.   
Karp, R.M., 1975. On the computational complexity of combinatorial problems. Networks 5, 45–68.   
Klenk, F., Kerndl, F., Heidinger, F., Benfer, M., Peukert, S., Lanza, G., 2022. Product allocation and network configuration in global production networks: An integrated optimization approach. Production Engineering 17. doi:10.1007/ s11740-022-01149-4.   
Kubal´ık, J., Kadera, P., Jirkovsk´y, V., Kurilla, L., Prokop, Sˇ., 2019. Plant layout optimization using evolutionary algorithms, in: Maˇr´ık, V., Kadera, P., Rzevski, G., Zoitl, A., Anderst-Kotsis, G., Tjoa, A.M., Khalil, I. (Eds.), Industrial Applications of Holonic and Multi-Agent Systems, Springer International Publishing, Cham. pp. 173–188.   
Layeb, A., Chenche, S., 2012. A novel grasp algorithm for solving the bin packing problem. International Journal of Information Engineering and Electronic Business 4, 8.   
Li, C., Han, Y., Zhang, B., Wang, Y., Li, J., Gao, K., 2025. A novel multi-objective hybrid evolutionary algorithm based on variable weight strategy for distributed hybrid flowshop scheduling with batch processing machines and variable sublots. Applied Soft Computing 170, 112650. URL: https://www.sciencedirect.com/ science/article/pii/S1568494624014248, doi:https://doi.org/10.1016/j. asoc.2024.112650.   
Neiro, S.M., Madan, T., Pinto, J.M., Maravelias, C.T., 2022. Integrated produc

tion and distribution planning for industrial gases supply chains. Computers & Chemical Engineering 161, 107778. URL: https://www.sciencedirect.com/ science/article/pii/S0098135422001193, doi:https://doi.org/10.1016/j. compchemeng.2022.107778.

Park, Y.B., Kim, H.S., 2016. Simulation-based evolutionary algorithm approach for deriving the operational planning of global supply chains from the systematic risk management. Computers in Industry 83, 68–77. URL: https://www.

sciencedirect.com/science/article/pii/S0166361516301774, doi:https:// doi.org/10.1016/j.compind.2016.09.003.

Peng, J., Chen, L., Zhang, B., 2022. Transportation planning for sustainable supply chain network using big data technology. Information Sciences 609, 781–798. URL: https://www.sciencedirect.com/science/article/pii/ S0020025522008015, doi:https://doi.org/10.1016/j.ins.2022.07.112.

Psarommatis, F., Fraile, F., Ameri, F., 2023. Zero defect manufacturing ontology: A preliminary version based on standardized terms. Computers in Industry 145, 103832. URL: https://www.sciencedirect.com/science/article/pii/ S0166361522002287, doi:https://doi.org/10.1016/j.compind.2022.103832.

Quan, C., He, Q., Ye, X., Cheng, X., 2021. Optimization of the milk-run route for inbound logistics of auto parts under low-carbon economy. Journal of Algorithms & Computational Technology 15, 174830262110653. doi:10.1177/ 17483026211065387.

Scheuermann, A., Leukel, J., 2014. Supply chain management ontology from an ontology engineering perspective. Computers in Industry 65, 913–923. URL: https://www.sciencedirect.com/science/article/pii/ S0166361514000438, doi:https://doi.org/10.1016/j.compind.2014.02.009. Schirrmann, A., 2022. Simulation of industrial systems for next-generation aircraft manufacturing, in: Proceedings of the 2022 Winter Simulation Conference, AnyLogic. URL: https://www.anylogic.com/resources/articles/ simulation-of-industrial-systems-for-next-generation-aircraft-manufacturing/. Shearer, R.D., Motik, B., Horrocks, I., 2008. Hermit: A highly-efficient owl reasoner., in: Owled, p. 91. Shen, G., Luo, K., Li, L., 2020. A multi-plant production planning model considering non- repeated setup and aperiodic shipment. Journal of Manufacturing Sys

tems 57, 451–459. URL: https://www.sciencedirect.com/science/article/ pii/S0278612520301941, doi:https://doi.org/10.1016/j.jmsy.2020.11.007.

Vicente, J.J., 2025. Optimizing supply chain inventory: A mixed integer linear programming approach. Systems 13. URL: https://www.mdpi.com/2079-8954/13/ 1/33, doi:10.3390/systems13010033.

Wang, Q., Yu, X., 2014. Ontology based automatic feature recognition framework. Computers in Industry 65, 1041–1052. URL: https://www.sciencedirect.com/ science/article/pii/S0166361514000827, doi:https://doi.org/10.1016/j. compind.2014.04.004.

Wibawa, A., Mahmudy, W., Rizki, A.M., Yuliastuti, G.E., Tama, I., 2022. Multisite aggregate production planning using particle swarm optimization. Journal of Engineering, Project, and Production Management 12, 62–69. doi:10.32738/ jeppm-2022-0006.

Xue, Y., Ji, S., Zhu, G., Zhao, P., 2023. Solving the sustainable automobile production-distribution joint optimization in the physical internet-enabled hyperconnected order-to-delivery system by i-nsgaiii. IEEE Access 11, 7471–7494. doi:10.1109/ACCESS.2023.3237735.

Zhao, W., Liu, J., 2008. Owl/swrl representation methodology for express-driven product information model: Part i. implementation methodology. Computers in Industry 59, 580–589. URL: https://www.sciencedirect.com/science/article/ pii/S0166361508000213, doi:https://doi.org/10.1016/j.compind.2008.02. 002.

![](images/a3cbaa17dc1c7f39fda03b6c3b7c965ccade92632c07eaaa7294090892e50d12.jpg)  
Fig. 3. The production graph (top) and an example of a part of the optimized industrial system with details of the corresponding transportation graph (bottom). Arrows in the production graph represent the specialisation relation.

Listing 2. Assignment of production units to parts for the double sourcing scenario. For each part, two production units are assigned, each of them with a respective production share

# Part:singleaisle aircraft

# Part: s2 top shell

a）supplier:airbus operations France country: France site: l38 share: 0.33 b） supplier: airbus operations Germany country: Germany site: $l _ { 3 1 }$ share: 0.67 Part: s3 lower shell a)supplier: generic buy 7 country:Africa-c2 site: $l _ { 3 9 }$ share: 0.69 b） supplier: generic buy 3 country: Germany site: l28 share: 0.31

a） supplier: generic buy 5 country: EastAsia-c2 site: $l _ { 3 0 }$ share: 0.32 b） supplier:airbus operations Spain country: Spain site: $l _ { 7 }$ share: 0.68 Part: front landing gear a)supplier:airbus operations UK country:United Kingdom site: $l _ { 9 }$ share: 0.21 b） supplier: generic buy 19 country:France site: $l _ { 2 1 }$ share:0.79

· ·