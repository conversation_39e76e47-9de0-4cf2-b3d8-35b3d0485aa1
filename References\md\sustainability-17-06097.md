Article

# Assessing Renewable Energy Adoption to Achieve Sustainable Development Goals in Ha’il Region

Rabab Triki $\mathbf { 1 } _ { \textcircled { 1 } }$ , <PERSON><PERSON> $\pmb { 2 } \oplus \pmb { \imath }$ , <PERSON><PERSON><PERSON> $2 , * \textcircled { \mathbb { D } }$ and <PERSON> $\mathfrak { s } _ { \mathbb { O } }$

Academic Editor: <PERSON><PERSON><PERSON>ceived: 16 April 2025   
Revised: 27 June 2025   
Accepted: 30 June 2025   
Published: 3 July 2025

Citation: <PERSON><PERSON>, R.; <PERSON><PERSON>, S.M.; Bahou, Y.; Boudabous, M.M. Assessing Renewable Energy Adoption to Achieve Sustainable Development Goals in Ha’il Region. Sustainability 2025, 17, 6097. https://doi.org/ 10.3390/su17136097

Copyright: $\odot 2 0 2 5$ by the authors. Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (https://creativecommons.org/ licenses/by/4.0/).

1 Management Information Systems Department, Applied College, Ha’il University, P.O. Box 2440, Ha’il 55424, Saudi Arabia; <EMAIL>   
2 Computer Science Department, Applied College, Ha’il University, P.O. Box 2440, Ha’il 55424, Saudi Arabia; <EMAIL>   
3 Department of Information and Computer Science, College of Computer Science and Engineering, Ha’il University, P.O. Box 2440, Ha’il 55424, Saudi Arabia; <EMAIL>   
\* Correspondence: <EMAIL>

# Abstract

Today’s environmental issues are among the primary themes that researchers explore in their search for practical solutions to achieve the Sustainable Development Goals (SDGs), such as reducing carbon emissions and promoting sustainable practices. Renewable energy is crucial to overcoming future challenges, causing many countries to accelerate their adoption at various levels. In this context, the impact of renewable energy adoption on achieving the Sustainable Development Goals in the Ha’il region has been evaluated. Specifically, two techniques are employed. The first technique is an empirical model based on the Vector Error Correction Model (VECM), which identifies the SDGs related to renewable energy in achieving SDGH. Only three SDGs (SDG7, SDG12, and SDG13) were found to influence SDGH significantly. The second technique uses deep learning, specifically LSTM networks, to forecast SDGH behavior over a ten-year period about the three selected SDGs. The results indicate that these three SDGs play a crucial role in sustainable development in the Ha’il region. Therefore, this research produces strategic recommendations to optimize the adoption of renewable energy in the Ha’il region. These findings provide policymakers with a data-driven framework to enhance strategies, utilize resources more efficiently, and promote broader sustainability initiatives.

Keywords: renewable energy; SDGs; VECM; deep learning technique; LSTM networks; Ha’il region

# 1. Introduction

The Sustainable Development Goals (SDGs) highlight the crucial role of renewable energy in addressing global challenges [1]. In this context, the transition from finite and polluting fossil fuels to clean and renewable energy sources has become an urgent necessity [2]. Ensuring long-term economic resilience requires both mitigating climate change and improving energy security. Recognizing these imperatives, many countries now regard renewable energy as a fundamental pillar of their sustainable development strategies [3]. For example, Morocco has invested heavily in solar and wind energy, with projects such as the Noor Ouarzazate Solar Complex, positioning itself as a regional leader in renewable energy development [4].

As one of the leading oil producers in the world, Saudi Arabia has historically relied on hydrocarbons for economic growth [5]. However, in response to environmental concerns and the volatility of global oil markets, the country launched Vision 2030, a strategic plan to diversify its economy and energy sector. A key objective of this initiative is to generate $5 0 \%$ of the nation’s electricity from renewable sources by 2030, supported by substantial investments estimated at 705 billion Saudi riyals (https://www.iea.org “accessed on 27 January $2 0 2 5 ^ { \prime \prime }$ . The Saudi Green Initiative further strengthens these efforts by setting specific targets to reduce $\mathrm { C O } _ { 2 }$ emissions and expand sustainable energy solutions [6].

The Ha’il region is a strategic location for the adoption of renewable energy within this national strategy. In northern Saudi Arabia, Ha’il benefits from favorable climatic conditions for the development of solar and wind energy. The region receives annual solar irradiation exceeding $2 0 0 0 \mathrm { k W h / m } ^ { 2 }$ , making it highly suitable for photovoltaic power generation [7]. Additionally, some areas in Ha’il record wind speeds above $6 \mathrm { m } / \mathrm { s } .$ , indicating significant potential for wind energy projects (https://globalatlas.irena.org “accessed on 12 February $2 0 2 5 ^ { \prime \prime } .$ ). Therefore, Ha’il is particularly important as the region seeks to diversify its economy by attracting investments in clean energy, reducing dependence on fossil fuels, and creating new employment opportunities [8].

This study aims to evaluate the adoption of renewable energy in the Ha’il region and its potential contribution to achieving the SDGs. This study examines the existing regulatory framework, economic feasibility, and technological readiness of renewable energy projects in the area. This research aims to identify key factors that influence the implementation of clean energy solutions. The findings will provide policymakers and investors with valuable information and offer recommendations for the development of the Ha’il region. These recommendations align with Saudi Arabia’s local initiatives under Vision 2030 and its global commitment to sustainable development.

The remainder of this paper is structured as follows. Section 2 reviews the relevant literature. Section 3 presents the methodology. Section 4 discusses the results and analysis. Section 5 offers conclusions and recommendations.

# 2. Review of the Literature

To evaluate the adoption of renewable energy in the Ha’il region, it is essential to examine existing research on factors that influence its deployment, including regulatory frameworks, economic incentives, and technological advances. Several studies have explored these critical factors in detail. Reference [8] highlights the importance of regulatory adoption in promoting clean energy. Similarly, [9] emphasizes the role of economic incentives and investments in accelerating the energy transition. Reference [10] underscores the importance of investing in renewable energy in achieving the SDGs, while [11] identifies technological advances as key drivers of the expansion of renewable energy.

Furthermore, several researchers have examined the socio-economic and environmental benefits of transitioning to renewable energy. Reference [12] evaluated its impact on the achievement of the SDGs, focusing on its role in reducing carbon emissions and improving economic resilience. Reference [13] analyzed its potential to improve energy security and create employment opportunities. Reference [14] explored the mechanisms through which renewable energy influences the progress of the SDG, demonstrating that government incentives and regulatory frameworks are critical to accelerate or limit adoption. Reviewing these contributions positions this study within the existing literature and identifies the main drivers and challenges specific to the Ha’il region. Reference [15] surveyed the region of the Middle East and North Africa (MENA) and found a strong cultural awareness and willingness to support renewable energy. Their results highlight positive attitudes toward investing in clean energy for sustainable development.

Recent international and regional studies have confirmed a strong correlation between renewable energy deployment and the achievement of the SDGs. For example, [16] forecasts a tripling of renewable energy generation in South America by 2050, particularly in Brazil and Chile, which will contribute to achieving SDGs 7 and 13. In Southeast Asia, a comparative study by [17] reveals that countries such as Vietnam and China have reduced their emissions through efforts focused on renewable energy and forest preservation [17]. Similarly, [18] examines the Moroccan context and demonstrates the positive impact of renewable energy investment on economic growth and sustainable development. Reference [19] emphasizes the importance of investing in education and the country’s solar and wind potential to drive the energy transition.

Investment strategies in renewable energy markets have also been extensively studied. Reference [20] analyzed investment trends but did not address the unique financial constraints of inland regions, such as Ha’il. Reference [21] examined the public acceptance and social factors that influence the adoption of renewable energy; however, their findings, based on urban case studies, may not be fully applicable to less populated areas. A survey of 500 rural households by [14] found that the transition to renewable energy resulted in a $1 5 \%$ increase in local employment, which is particularly relevant for the socioeconomic context of rural areas. Similarly, [22] investigated the social acceptance of renewable energy in Saudi Arabia, reinforcing its importance in achieving the SDGs. These studies provide valuable information on the socioeconomic aspects of the adoption of renewable energy and highlight the importance of considering region-specific factors when evaluating the potential of renewable energy in areas such as Ha’il.

Despite these contributions, limited research has explored region-specific challenges, particularly in the Ha’il region. This gap highlights the need for further investigation to determine which SDGs are most relevant to the region and how local authorities can effectively prioritize them for the integration of renewable energy. In [23], using a panel data model, researchers found that renewable energy investments significantly advanced SDG7 and SDG13 in 30 developing countries and showed a positive correlation between increased renewable capacity and economic resilience. Similarly, [24] used a structural equation model to evaluate renewable energy policies within the European Union. Their findings reveal that supportive policy frameworks, such as subsidies and tax incentives, helped achieve SDG8 and SDG9, particularly in countries with a high penetration of renewable energy. However, their focus on industrialized economies implies that different policy approaches may be required for regions such as Ha’il, which face different economic and environmental conditions.

This study builds on existing research that employs empirical and deep learning approaches to assess the adoption of renewable energy in the Ha’il region and its potential contribution to achieving the most relevant SDGs. The following section outlines the methodology used to determine the impact of renewable energy adoption on achieving the Sustainable Development Goals in the Ha’il region.

# 3. Methodology

This study uses two steps to assess the impact of the adoption of renewable energy in SDGH. First, an empirical approach is used to analyze the effects of related renewable energy SDGs on SDGH [25,26]. Next, a deep learning technique is used to forecast the future impact of the SDGs, as determined by SDGH.

# 3.1. Step 1: Empirical Approach

The empirical approach is structured into five distinct phases. Initially, the presented data and descriptive statistics are reviewed. Subsequently, stationarity is evaluated through the application of the ADF and PP tests. Johansen Cointegration analysis is then conducted to ascertain the presence of a long-term relationship. The final phase involves the selection of the most appropriate model through time series analysis. Prior to the detailed explanation of each technique, the most relevant SDGs associated with renewable energy in the Ha’il region must be identified.

Overview of selected SDGs: The primary SDGs associated with renewable energy were reviewed, as outlined in Table 1.

Table 1. Description of variables.   

<html><body><table><tr><td>Variables</td><td>Descriptions</td><td>Sources</td><td>References</td></tr><tr><td>SDG6</td><td>Clean water and sanitation</td><td>World Development</td><td>[27,28]</td></tr><tr><td>SDG7</td><td>Affordableand clean energy</td><td>Indicators/Official Statistics KSA (https:/ /data.worldbank.org/</td><td>[27,29]</td></tr><tr><td>SDG12</td><td>Responsible consumption and production</td><td>country/saudi-arabia & https:/ /www.stats.gov.sa</td><td>[30,31]</td></tr><tr><td>SDG13</td><td>Climate and action</td><td>"accessed on 29 January 2025")</td><td>[29,31]</td></tr><tr><td>SDG15</td><td>Life on land</td><td></td><td>[28,31]</td></tr></table></body></html>

Data and descriptive statistics: In this phase, a yearly time series of five variables from the Ha’il region was used, spanning 2002 to 2023 (see Table 1). Next, EViews (version 12) was used to identify specific SDGs relevant to achieving SDGs in the Ha’il region. Furthermore, a descriptive analysis of the time series variables for the Ha’il region is provided in Table 2. The Jarque–Bera test was subsequently applied to assess the normality of the variables. As a result, all variables were non-normal, although not all were statistically significant, since the probability value exceeded 0.05. Consistent with this observation, all variables demonstrating kurtosis have values less than 3. This indicates that they are platykurtic, meaning that their tails are lighter than those of a normal distribution. Except for SDG12 (0.015) and SDGH (0.922), all variables showed negative skewness. This indicates that the “tail” of all variables, except for SDG12 and SDG15, is skewed to the left. This occurred because the skewness values were either negative or positive.

Table 2. Results of descriptive statistics and correlation matrix.   

<html><body><table><tr><td></td><td>SDG6</td><td>SDG7</td><td>SDG12</td><td>SDG13</td><td>SDG15</td><td>SDGH</td></tr><tr><td>Mean</td><td>4.562</td><td>4.656</td><td>2.406</td><td>1.477</td><td>1.516</td><td>4.697</td></tr><tr><td>Median</td><td>4.589</td><td>4.705</td><td>2.367</td><td>1.572</td><td>1.518</td><td>4.724</td></tr><tr><td>Maximum</td><td>4.603</td><td>5.020</td><td>2.880</td><td>1.790</td><td>1.598</td><td>6.097</td></tr><tr><td>Minimum</td><td>4.265</td><td>4.154</td><td>1.847</td><td>0.772</td><td>1.372</td><td>2.388</td></tr><tr><td>Std. Dev.</td><td>0.093</td><td>0.270</td><td>0.354</td><td>0.298</td><td>0.057</td><td>0.781</td></tr><tr><td>Skewness</td><td>-2.823</td><td>-0.809</td><td>0.015</td><td>-0.852</td><td>-1.068</td><td>0.922</td></tr><tr><td>Kurtosis</td><td>9.046</td><td>2.449</td><td>1.541</td><td>2.740</td><td>3.634</td><td>4.718</td></tr><tr><td>Jarque-Bera</td><td>62.742</td><td>2.681</td><td>1.951</td><td>2.725</td><td>4.558</td><td>5.828</td></tr><tr><td>Probability</td><td>0.000</td><td>0.261</td><td>0.376</td><td>0.255</td><td>0.102</td><td>0.054</td></tr><tr><td>SDG6</td><td>1</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>SDG7</td><td>0.030</td><td>1</td><td></td><td></td><td></td><td></td></tr><tr><td>SDG12</td><td>0.488</td><td>0.433</td><td>1</td><td></td><td></td><td></td></tr><tr><td>SDG13</td><td>0.440</td><td>-0.281</td><td>0.887</td><td>1</td><td></td><td></td></tr><tr><td>SDG15</td><td>0.655</td><td>-0.175</td><td>0.657</td><td>0.717</td><td>1</td><td></td></tr><tr><td>SDGH</td><td>0.539</td><td>0.762</td><td>0.764</td><td>0.660</td><td>0.346</td><td>1</td></tr></table></body></html>

Additionally, the correlation results in Table 2 indicate that most of the variable relationships with SDG7 are moderate, ranging from 0.3 to 0.7. Furthermore, the relationship between SDG7 and SDG12 with SDGH indicates a strong correlation. These results indicate that there is no bi-variable multicollinearity problem.

The correlations suggest that there is a weak positive relationship between SDG7 and SDG6, with a correlation coefficient of 0.030. SDG12 and SDG13 exhibit a strong positive correlation of 0.887, which implies that progress in one may be linked to progress in the other. The negative correlation between SDGH and the other SDGs indicates that it may behave inversely with them. Furthermore, most variable pairs have a low correlation, except for a notable correlation of $7 6 \%$ between SDGH and SDG7. A significant correlation was also identified between SDGH and both SDG12 and SDG13. Based on this, the stationarity test (unit root) was performed, followed by the cointegration test, to explore the long-term relationships among the variables.

Stationarity test: In this analysis phase, stationarity was assessed using the Unit Root Test with the ADF and PP tests to determine the order of integration of the variables under consideration. The ADF test checks whether a time series is stationary by testing for a unit root [32]. Similarly, the PP test is an alternative to the ADF test that adjusts for autocorrelation and heteroskedasticity in errors [33].

Cointegration test: This test assesses whether a long-run equilibrium relationship exists between non-stationary time series [34]. After confirming the cointegration between the selected SDGs and SDGH, the Vector Error Correction Model (VECM) is applied to examine both the short-term dynamics and the long-run equilibrium relationships.

Lag selection and VECM specification: To estimate the VECM, the optimal lag length was first determined, as selecting an appropriate lag order is crucial to ensure a good fit of the model. After identifying the optimal lag, the following hypothesis was subsequently tested within the VECM framework.

$\mathbf { H } _ { 0 }$ : The error correction term is not significant, implying that there is no long-run adjustment to equilibrium.   
$\mathbf { H _ { 1 } }$ : The error correction term is significant, indicating that deviations from long-run equilibrium are corrected over time.

The VECM models the long-term equilibrium among the variables [35]. The model is represented as follows:

$$
S D G H _ { t } = \beta _ { 0 } + \beta _ { 1 ^ { * } } S D G 6 _ { t } + \beta _ { 2 ^ { * } } S D G 7 _ { t } + \beta _ { 3 ^ { * } } S D G 1 2 _ { t } + \beta _ { 4 ^ { * } } S D G 1 3 _ { t } + \beta _ { 5 ^ { * } } S D G 1 5 _ { t } + \varepsilon _ { t }
$$

where

t is $1 , 2 , . . . , \mathrm { T } ;$   
$s D G H _ { t }$ is the column vector of the K-dimensional endogenous variable;   
$S D G _ { 6 } , S D G _ { 7 } , S D G _ { 1 2 } , S D G _ { 1 3 } .$ , and $s D G _ { 1 5 }$ are the column vector of the dimensional exogenous variables;   
$\beta _ { 0 } \ldots \beta _ { 5 }$ are the coefficients of long-term equilibrium;   
t denotes the time period;   
$\scriptstyle { \varepsilon _ { t } }$ is the stationary cointegration error term.

The VECM equation can then be represented as follows:

$$
\begin{array} { l } { \displaystyle { { \mathrm { \it ~ \hat { \mu } D G H } } _ { t } = \alpha _ { 0 } + \sum _ { i = 1 } ^ { n } \Delta { \mathrm { L n S } } D G H _ { ( t - i ) } + \sum _ { i = 1 } ^ { n } \Delta { \mathrm { L n S } } D G 6 _ { ( t - i ) } + \sum _ { i = 1 } ^ { n } \Delta { \mathrm { L n S } } D G 7 _ { ( t - i ) } + \sum _ { i = 1 } ^ { n } \Delta { \mathrm { L n S } } D G 1 2 _ { ( t - i ) } } } \\ { \displaystyle ~ + \sum _ { i = 1 } ^ { n } \Delta { \mathrm { L n S } } D G 1 3 _ { ( t - i ) } + \sum _ { i = 1 } ^ { n } \Delta { \mathrm { L n S } } D G 1 5 _ { ( t - i ) } + \varepsilon _ { t } } \end{array}
$$

where

$\Delta$ represents the first difference operator;   
$\scriptstyle \alpha _ { 0 }$ is the intercept term;   
$\mathbf { \Omega } _ { n }$ is the number of lags in the model.

# 3.2. Step 2: Deep Learning Technique

The empirical model from the previous step has allowed us to identify three SDGs (i.e., SDG7, SDG12, and SDG13) that influence the sustainable development of the Ha’il region (i.e., SDGH) if renewable energy is adopted.

In this section, the three SDGs are forecasted over the next ten years using the deep learning technique to analyze their reactions to SDGH. To achieve this, LSTM networks were selected to forecast the behavior of SDGH in relation to the chosen SDGs. LSTM networks are a type of Recurrent Neural Network (RNN) specifically designed to process sequential data and capture long-term dependencies for two reasons. The first reason is the non-linear nature of our data (i.e., asymmetric), as demonstrated in Section 3.1. Traditional methods, such as ARIMA, are inefficient when forecasting based on historical data of an asymmetric nature [36]. The second reason is the efficiency of LS networks in forecasting time series [37]. This is due to their ability to learn and retain long-term dependencies when analyzing time series.

Our proposed model consists of three phases, as illustrated in Figure 1.

![](images/9270873f04bd20124f50c544ae2a8336c8bdf33680626f72466f844644db9d86.jpg)  
Figure 1. Proposed model for forecasting the four SDGs in the next 10 years.

Data preprocessing phase: This phase involves normalizing the data using the Min–Max Scaling technique, which converts the data into the interval [0,1]. This method is used to maintain numerical stability and enhance convergence. To preserve the temporal structure of the time series, the dataset was chronologically split into $8 0 \%$ training, $1 0 \%$ validation, and $1 0 \%$ testing, without scalability.

Training phase: Training was performed using the Adam optimizer with an initial learning rate of 0.001. To avoid overfitting, dropout layers with a rate of 0.2 were applied after each LSTM layer. The ReLU activation function was used in the hidden layers, while the sigmoid function was applied in the output layer, since the target values were normalized to a range of 0 to 1. Additionally, an early stopping mechanism was implemented based on the validation loss, with a patience of 10 epochs, and automatic restoration of the best weights observed during training.

Furthermore, both the training and validation data, along with the LSTM network, were utilized to train the model, remarking that the LSTM network consisted of four layers:

Input layer: This layer consists of four neurons (units). Each neuron represents a time series associated with one of the four SDGs (i.e., SDG7, SDG12, SDG13, and SDGH).

First hidden layer (LSTM1): Two configurations, 50 and 100 neurons, were tested in this first hidden layer, using the first dropout layer to prevent overlearning during training.

Second hidden layer (LSTM2): In this layer, 25 and 50 neurons were tested with a second dropout layer.

Dense layer (output): This layer consists of four neurons, each representing a time series associated with one of the four SDGs.

To improve model performance and prevent overfitting, a systematic model tuning process was implemented. Several configurations were tested, varying the number of LSTM units (50 or 100 in the first layer, and 25 or 50 in the second), the number of training epochs (50 or 100), and batch sizes (8, 16, 32, and 64), while maintaining a fixed dropout rate of 0.2 and the Adam optimizer with a learning rate of 0.001. The best-performing architecture was selected based on model performance metrics calculated using the validation set. Performance was evaluated using the Mean Squared Error (MSE) and the coefficient of determination $( \mathbb { R } ^ { 2 } )$ . If the MSE indicator is equal, the ${ \mathrm { R } } ^ { 2 }$ indicator is compared, and the model with the closest value to 1 is selected. Additionally, an early stopping mechanism based on validation loss with a patience of 10 epochs was applied. The best results for each batch are shown in Table 3.

Table 3. Best results for each batch in 32 training models.   

<html><body><table><tr><td>Batches</td><td>Epochs</td><td>LSTM1</td><td>LSTM2</td><td>MSE</td><td>R²</td></tr><tr><td>8</td><td rowspan="4">100</td><td rowspan="4">100</td><td rowspan="4">50</td><td>0.003309</td><td>0.935892</td></tr><tr><td>16</td><td>0.004126</td><td>0.925803</td></tr><tr><td>32</td><td>0.004813</td><td>0.909592</td></tr><tr><td>64</td><td>0.007951</td><td>0.857547</td></tr></table></body></html>

According to Table 3 it was concluded that for each batch size, the optimal hyperparameters are LSTM1 with 100 neurons, LSTM2 with 50 neurons, and 100 epochs, based on the MSE and $\scriptstyle \mathrm { R } ^ { 2 }$ metrics. Figure 2 illustrates the training results obtained with the best hyperparameters.

![](images/5137ea4be7bbe3961c1424e1fa1fc9cb9679cc2ceb108bc0eba4ec327cf10fac.jpg)  
Figure 2. Loss curves for learning and validation of the proposed model for SDG forecasting.

According to Figure 2, the minimum MSE is 0.003309, achieved by batch 8 at epoch 44. Therefore, the hyperparameters used for forecasting are summarized in Table 4.

Table 4. Hyperparameters for SDG forecasting.   

<html><body><table><tr><td>LSTM1</td><td>100</td></tr><tr><td>LSTM2</td><td>50</td></tr><tr><td>Batch</td><td>8</td></tr><tr><td>Epoch</td><td>100</td></tr></table></body></html>

# 4. Results and Analysis

# 4.1. Empirical Findings

Stationarity results: In this analysis phase, a stationarity test (Unit Root Test) is performed using ADF and PP tests to determine the order of integration of the variables under consideration. The test results presented in Table 5 indicate that none of the variables is integrated at the first level; however, after the first difference, they become stationary.

Table 5. Results of the Unit Root Test.   

<html><body><table><tr><td rowspan="2">Variable</td><td colspan="2">ADF</td><td colspan="2">PP</td></tr><tr><td>Intercept (0)</td><td>Intercept I (1)</td><td>Intercept I (0)</td><td>Intercept I (1)</td></tr><tr><td>SDGH</td><td>0.8960</td><td>0.0000 ***</td><td>0.0071</td><td>0.0000 ***</td></tr><tr><td>SDG6</td><td>0.0000 ***</td><td>0.0014 **</td><td>0.0000</td><td>0.0014 **</td></tr><tr><td>SDG7</td><td>0.0491 **</td><td>0.0015 **</td><td>0.9120</td><td>0.0015 **</td></tr><tr><td>SDG12</td><td>0.5780</td><td>0.0220 **</td><td>0.5780</td><td>0.0208 **</td></tr><tr><td>SDG13</td><td>0.6607</td><td>0.0135 **</td><td>0.6887</td><td>0.0000 ***</td></tr><tr><td>SDG15</td><td>0.0661 ***</td><td>0.0003 ***</td><td>0.0661</td><td>0.0003 ***</td></tr></table></body></html>

\*\*, and \*\*\* denote significance levels of $1 \%$ , ${ \overline { { 5 \% } } } ,$ and $1 0 \%$ , respectively. In the ADF test, the probability values for testing the null hypothesis that the series has unit root and lag length are based on SIC.

Cointegration results: The Johansen Cointegration suggests the following hypothesis:

$\mathbf { H } _ { 0 }$ : There is no cointegration between the SDGs and SDGH;   
$\mathbf { H _ { 1 } }$ : A cointegration exists between the SDGs and SDGH.

Table 6 presents the results of this test, indicating the number of cointegrating vectors identified.

Table 6. Results of the Johansen Cointegration test.   

<html><body><table><tr><td>Hypothesized</td><td rowspan="3">Eigenvalue</td><td rowspan="3">Trace Statistics</td><td rowspan="3">5% CV</td><td rowspan="3">Probability *</td></tr><tr><td>No. of CE(s)</td></tr><tr><td></td></tr><tr><td>None *</td><td>0.999719</td><td>323.4694</td><td>95.75366</td><td>0.0000</td></tr><tr><td>At most 1 *</td><td>0.988926</td><td>159.9534</td><td>69.81889</td><td>0.0000</td></tr><tr><td>At most 2 *</td><td>0.834967</td><td>69.88991</td><td>47.85613</td><td>0.0001</td></tr><tr><td>At most 3 *</td><td>0.649225</td><td>33.85768</td><td>29.79707</td><td>0.0161</td></tr><tr><td>At most 4</td><td>0.433851</td><td>12.90548</td><td>15.49471</td><td>0.1183</td></tr><tr><td>At most 5</td><td>0.073532</td><td>1.527526</td><td>3.841465</td><td>0.2165</td></tr></table></body></html>

The trace test indicates four cointegration equations at the 0.05 level, and \* denotes rejection of the hypothesis at the 0.05 level.

The cointegration results confirm four cointegration relationships (probability $< 0 . 0 5 \rangle$ among the variables included in the model. Specifically, this finding indicates that SDGH maintains an equilibrium condition with each variable in the long-run. Consequently, the Alternative Hypothesis (H1) is confirmed. There is a long-run relationship between the SDGs (SDG6, SDG7, SDG12, SDG13, and SDG15) and SDGH. It is concluded that the variables are cointegrated, which justifies the use of a VECM instead of a standard VAR in the first differences.

VECM estimation results and robustness test: The results of the lag selection criteria, including the AIC, SC, and FPE criteria, are presented in Table 7.

Table 7. VAR lag length criteria.   

<html><body><table><tr><td>Lag</td><td>LogL</td><td>LR</td><td>FPE</td><td>AIC</td><td>SC</td><td>HQ</td></tr><tr><td>0</td><td>313.0308</td><td>NA</td><td>1.87 × 10-21</td><td>-30.70308</td><td>-30.40436</td><td>-30.64476</td></tr><tr><td>1</td><td>397.0467</td><td>109.2208 *</td><td>1.85 ×10-23</td><td>-35.50467</td><td>-33.41364 *</td><td>-35.09648</td></tr><tr><td>2</td><td>450.3009</td><td>37.27790</td><td>1.23 × 10-23 *</td><td>-37.23009 *</td><td>-33.34673</td><td>-36.47202 *</td></tr></table></body></html>

\*: the lag order chosen by the criterion; LR: sequential modified LR test statistic (each test at the $\overline { { 5 \% } }$ level); FPE: Final Prediction Error; AIC: Akaike Information Criterion; SC: Schwartz Criterion; HQ: Hanan–Queen.

According to the results of the lag length selection criteria for the VECM, which include Log L, LR, FPE, AIC, SC, and HQ, the optimal lag order selected is 2, marked by an asterisk (\*). This choice is based on the FPE, AIC, and HQ criteria, indicating that lag 2 provides the best model fit. The final step is to estimate the VECM, which uncovers the long-term and short-term relationships between the SDGs and SDGH. The results of the VECM estimate are presented in Table 8, which includes the estimated coefficients, error correction terms, and statistical significance levels.

The VECM analysis in Table 8 shows that, in the long run, SDG7 and SDG12 have negative and significant coefficients $( - 0 . 1 5 1 2 7 3$ at $5 \%$ and $- 0 . 0 6 1 0 9 6$ at $1 0 \%$ , respectively), indicating an equilibrium relationship, although SDG12 adjusts more slowly. In comparison, SDG13 has a positive coefficient (0.058779 at $5 \%$ ), suggesting a potential deviation from equilibrium. In the short term, SDG7 and SDG12 demonstrate significant negative effects (−0.115264 and $- 0 . 0 6 5 1 2 9$ at $5 \%$ , respectively), implying that past values negatively affect their current levels, while SDG13 has a strong negative coefficient $( - 0 . 3 7 5 6 4 7$ at $1 \%$ ), confirming that improvements in SDG7 contribute to reducing emissions and supporting climate action. The model exhibits strong explanatory power, with ${ \mathrm { R } } ^ { 2 }$ values of 0.9941 for SDG7, 0.9874 for SDG12, and 0.9787 for SDG13, indicating that more than $9 7 \%$ of the variations in these variables are well explained.

Additionally, the robustness analysis in Table 9 confirms the statistical reliability of the model, showing no signs of heteroskedasticity (as indicated by the F-statistic) or autocorrelation (as evidenced by the Durbin–Watson statistic, which is close to 2). The AIC and BIC values further demonstrate a well-fitting model, increasing confidence in the results. However, comparing these findings with alternative models could provide additional assurance regarding the robustness and stability of the model.

Building on the VECM analysis, the next step focuses on evaluating the adoption of renewable energy to achieve sustainable development in the Ha’il region, utilizing deep learning techniques with LSTM (Long Short-Term Memory) networks for forecasting and further analysis.

Table 8. Estimates of the VECM parameters.   

<html><body><table><tr><td colspan="7"></td></tr><tr><td>Error Correction</td><td>(1)D(SDGH)</td><td>(2)D(SDG6)</td><td>(3)D(SDG7)</td><td>(4)D(SDG12)</td><td>(5)D(SDG13)</td><td>(6)D(SDG15)</td></tr><tr><td>Cointegration</td><td>-0.088317</td><td>-0.016272</td><td>-0.151273</td><td>-0.061096</td><td>0.058779</td><td>-0.081685</td></tr><tr><td>Eq</td><td>(0.08189) ***</td><td>(0.53446)</td><td>(0.02380) **</td><td>(0.00072) *</td><td>(0.00315) **</td><td>(0.00067) *</td></tr><tr><td></td><td>0.013240</td><td>0.012941</td><td>0.103801</td><td>0.023319</td><td>0.210098</td><td>0.168751</td></tr><tr><td rowspan="3">SDGH(-1)</td><td>(0.02313) **</td><td>(1.49737)</td><td>(0.07119) ***</td><td>(0.05104) **</td><td>(0.22724)</td><td>(0.19546)</td></tr><tr><td>[0.05722]</td><td>[0.00864]</td><td>[1.45811]</td><td>[0.45688]</td><td>[0.92455]</td><td>[0.86336]</td></tr><tr><td>0.225545</td><td>-0.514771</td><td>0.078171</td><td>0.020161</td><td>-0.80661</td><td>0.316920</td></tr><tr><td>SDGH(-2)</td><td>(0.027614) **</td><td>(1.78710)</td><td>(0.08496) ***</td><td>(0.06091) ***</td><td>(0.027121) **</td><td>(0.23328)</td></tr><tr><td rowspan="3">SDG6(-1)</td><td>[0.81679]</td><td>[-0.28805]</td><td>[0.92005]</td><td>[0.33097]</td><td>[-2.97407]</td><td>[1.35855]</td></tr><tr><td>7.802626</td><td>-18.66631</td><td>-6.798718</td><td>-3.774512</td><td>-12.23363</td><td>5.499433</td></tr><tr><td>(5.35098)</td><td>(34.6306)</td><td>(1.64643)</td><td>(1.18041)</td><td>(5.25560)</td><td>(4.52048)</td></tr><tr><td rowspan="4">SDG6(-2)</td><td>[1.45817]</td><td>[-0.53901]</td><td>[-4.12938]</td><td>[-3.19764]</td><td>[-2.32773]</td><td>[1.21656]</td></tr><tr><td>-5.020864</td><td>11.70110</td><td>-4.811658</td><td>-3.060355</td><td>2.435546</td><td>4.641323</td></tr><tr><td>(4.54312)</td><td>(29.4022)</td><td>(1.39786)</td><td>(1.00220)</td><td>(4.46214)</td><td>(3.83801)</td></tr><tr><td>[-1.10516]</td><td>[0.39797]</td><td>[-3.44216]</td><td>[-3.05365]</td><td>[0.54582]</td><td>[1.20931]</td></tr><tr><td rowspan="4">SDG7(-1)</td><td>0.185664</td><td>0.281626</td><td>-0.115264</td><td>-0.065129</td><td>-0.375647</td><td>0.004023</td></tr><tr><td>(0.01153) *</td><td>(0.74662)</td><td>(0.03550) **</td><td>(0.02545) **</td><td>(0.11331)</td><td>(0.09746) ***</td></tr><tr><td>[1.60936]</td><td>[0.37720]</td><td>[-3.24720] -0.054589</td><td>[-2.55918]</td><td>[-3.31526]</td><td>[0.04128]</td></tr><tr><td>-0.234998</td><td>-0.27483 (0.63144)</td><td></td><td>-0.0381</td><td>0.095127</td><td>0.169734</td></tr><tr><td rowspan="4">SDG7(-2) SDG12(-1)</td><td>(0.09757) ***</td><td>[-0.43525]</td><td>(0.03002) **</td><td>(0.02152) **</td><td>(0.09583) *** [0.99268]</td><td>(0.08242) ***</td></tr><tr><td>[-2.40857]</td><td>40.68929</td><td>[-1.81841] 12.85526</td><td>[-1.77020]</td><td>27.72933</td><td>[2.05926]</td></tr><tr><td>-17.18332 (0.03383) **</td><td>(0.33797) **</td><td>(0.48866)</td><td>7.159117 (2.50120)</td><td></td><td>-11.29688</td></tr><tr><td></td><td>[0.55450]</td><td></td><td>[2.86227]</td><td>(11.1363)</td><td>(9.57859)</td></tr><tr><td rowspan="4">SDG12(-2)</td><td>[-1.51550]</td><td>-23.50239</td><td>[3.68487]</td><td>6.194075</td><td>[2.49000] -1.730149</td><td>[-1.17939]</td></tr><tr><td>9.740122</td><td></td><td>9.733476</td><td></td><td>(0.06586) ***</td><td>-9.494853</td></tr><tr><td>(0.023038) **</td><td>(0.97374)</td><td>(0.08407) ***</td><td>(0.03619) **</td><td></td><td>(7.79779)</td></tr><tr><td>[1.05522]</td><td>[-0.39343]</td><td>[3.42720]</td><td>[3.04199]</td><td>[-0.19084]</td><td>[-1.21763]</td></tr><tr><td rowspan="3">SDG13(-1)</td><td>0.098568</td><td>-0.167356</td><td>0.001300</td><td>-0.000938</td><td>0.000381</td><td>0.119493</td></tr><tr><td>(0.07687) ***</td><td>(0.49747)</td><td>(0.02365) **</td><td>(0.01696) **</td><td>(0.07550) ***</td><td>(0.06494) ***</td></tr><tr><td>[1.28232]</td><td>[-0.33642]</td><td>[0.05496]</td><td>[-0.05531]</td><td>[0.00505]</td><td>[1.84014]</td></tr><tr><td rowspan="3">SDG13(-2)</td><td>-0.018548</td><td>-0.155833</td><td>-0.008735</td><td>-0.00746</td><td>0.073357</td><td>-0.060245</td></tr><tr><td>(0.07056) ***</td><td>(0.45666)</td><td>(0.02171) **</td><td>(0.01557) *</td><td>(0.06930) **</td><td>(0.05961) **</td></tr><tr><td>[-0.26287]</td><td>[-0.34125]</td><td>[-0.40235]</td><td>[-0.47925]</td><td>[1.05849]</td><td>[-1.01066]</td></tr><tr><td rowspan="4">SDG15(-1)</td><td>0.344479</td><td>-1.042552</td><td>-0.095652</td><td>-0.031946</td><td>0.014823</td><td>0.653802</td></tr><tr><td>(0.28017)</td><td>(1.81321)</td><td>(0.08620)</td><td>(0.06180)</td><td>(0.27518)</td><td>(0.23669)</td></tr><tr><td>[1.22954]</td><td>[-0.57498]</td><td>[-1.10959]</td><td>[-0.51689]</td><td>[0.05387]</td><td>[2.76232]</td></tr><tr><td>-0.408106</td><td>0.166635</td><td>0.062039</td><td>0.014387</td><td>0.717860</td><td>-0.859204</td></tr><tr><td rowspan="3">SDG15(-2)</td><td>(0.39432)</td><td>(2.55194)</td><td>(0.12133)</td><td>(0.08698) ***</td><td>(0.38729)</td><td>(0.33312)</td></tr><tr><td>[-1.03497]</td><td>[0.06530]</td><td>[0.51134]</td><td>[0.16540]</td><td>[1.85356]</td><td>[-2.57929]</td></tr><tr><td>0.108697</td><td>0.215977</td><td>-0.023949</td><td>-0.004058</td><td>0.098421</td><td>-0.020553</td></tr><tr><td rowspan="2">C R-squared</td><td>(0.03700)</td><td>(0.23945)</td><td>(0.01138)</td><td>(0.00816)</td><td>(0.03634)</td><td>(0.03126)</td></tr><tr><td>0.764259</td><td>0.471703</td><td>0.994100</td><td>0.987438</td><td>0.978671</td><td>0.792686</td></tr><tr><td>Adj. R-squared</td><td>0.360131</td><td>-0.433948</td><td>0.983984</td><td>0.965902</td><td>0.942108</td><td>0.437290</td></tr></table></body></html>

\*, \*\*, and \*\*\* indicate the rejection of the hypothesis at the $1 \%$ , $5 \% _ { - }$ and $1 0 \%$ significance levels. Furthermore, square brackets and parentheses represent the t-statistic and the probability, respectively.

Table 9. Robustness of the VECM.   

<html><body><table><tr><td>Test</td><td>F-Statistic</td><td>Remarks</td></tr><tr><td>F-statistic</td><td>7.991393</td><td rowspan="3">No Heteroskedasticity</td></tr><tr><td>Prob (F-stat)</td><td>0.0086</td></tr><tr><td>Durbin-Watson</td><td>1.896138</td></tr><tr><td>Akaike Information Criterion</td><td colspan="2">No Autocorrelation -32.21675</td></tr><tr><td>Schwarz Criterion</td><td colspan="2">-28.33339</td></tr></table></body></html>

# 4.2. Test and Forecast Results

The testing phase was conducted using $1 0 \%$ of the dataset, following an 80/10/10 chronological split. This phase enables us to evaluate the model’s ability to generalize to unseen data before making future forecasts. Performance on the test set was evaluated using MSE and $\mathrm { R } ^ { 2 }$ . The results showed good agreement between the predicted and actual values, indicating that the model is reliable and well-calibrated for time series forecasting in the context of sustainable development indicators.

Furthermore, the forecast phase incorporates the optimal hyperparameters generated in the previous phase along with the LSTM network to evaluate the model and predict the four SDGs. The projected outcomes for the four SDGs over the next ten years are illustrated in Figure 3. Note that confidence intervals of $\pm 5 \%$ around the predicted values were included in the forecast figures to provide an estimate of prediction uncertainty.

![](images/4e78be274955a806e00c8f32da5ffd0d61b18b22870e9fbcf49523092bf52f02.jpg)  
Figure 3. Forecasting the SDGs over the next ten years with a $\pm 5 \%$ margin of error.

This graph was generated using the proposed model, which is likely based on a trend forecasting approach for the SDGs between 2024 and 2033. The model analyzed historical data to predict future trends in SDGH related to SDG7, SDG12, and SDG13. The higher values suggest an expected improvement in SDGH, with variations reflecting the uncertainty of the forecast. The shaded areas around the curves represent confidence intervals, indicating the reliability of the predictions. Some fluctuations, especially around 2028 and 2031, may result from economic or political events simulated by the model. SDGH follows an upward trajectory, suggesting general progress toward sustainable development in the Ha’il region. SDG7 and SDG12 gradually converge towards SDGH, indicating that clean energy and responsible consumption make positive contributions to sustainability goals. However, SDG13 consistently lags, highlighting a potential delay in climate action that may hinder the full implementation of SDGH. The sequential nature of the RNN enables it to capture long-term trends; however, the reliability of these forecasts depends on the policies implemented by the authorities. A deeper analysis of the factors that affect SDG13 may be necessary to enhance its alignment with SDGH and ensure complete development in Ha’il.

To complement the forecasting analysis, we now provide practical examples of renewable energy deployment that could be realistically implemented in the Ha’il region.

Considering the renewable energy potential in the Ha’il region, several practical options can be explored. For example, large-scale solar farms utilizing photovoltaic panels could be constructed on the region’s extensive desert land, capitalizing on the high solar irradiance levels throughout the year. Additionally, small hybrid systems that combine solar and wind energy can be installed in remote villages or agricultural areas to provide off-grid electricity and reduce reliance on diesel generators. Rooftop solar panels on public buildings, schools, and healthcare facilities could also serve as pilot projects to promote decentralized energy solutions. These options not only support the goals of the Saudi Green Initiative and Vision 2030 but also offer scalable models for sustainable development in similar arid and semi-arid regions.

# 5. Conclusions and Recommendations

This paper aims to assess the adoption of renewable energy and its potential to achieve the SDGs in the Ha’il region. To achieve this objective, two complementary methodologies were used. The first was an empirical analysis using the VECM, while the second involved the deep learning technique to explore future scenarios based on LSTM networks. The essential SDGs influenced by the adoption of renewable energy were identified through the adoption of renewable energy, based primarily on a yearly time series that covers 2002 to 2023 for five SDGs in the Ha’il region. Consequently, two types of contributions were highlighted: theoretical contributions and managerial implications.

Theoretical contribution: The econometric results highlight SDG7 (Affordable and Clean Energy), SDG12 (Responsible Consumption and Production), and SDG13 (Climate and Action) as the most critical for the sustainable development of Ha’il. The robustness of these findings is confirmed by statistical tests, including the Breusch–Pagan test for heteroskedasticity (F-statistic: 7.991, $p$ -value: 0.086), the Durbin–Watson statistic (1.896) for autocorrelation, and model fit indicators such as the Akaike Information Criterion $( - 3 2 . 2 1 6 )$ and Schwartz Criterion (−28.333). The results of the deep learning technique reinforce the econometric conclusions, validating the importance of these three SDGs in the pursuit of sustainability efforts. This study’s findings integrate econometric and deep learning approaches, supplementing existing research and enhancing theoretical insights by illustrating the direct impact of renewable energy adoption on sustainability goals in developing regions. Furthermore, the study combines traditional econometric analysis with deep learning to leverage its strengths in policy evaluation and forecasting.

Managerial implications: From a managerial perspective, the findings provide actionable information for policymakers, businesses, and community stakeholders in the Ha’il region. The results underscore the need for targeted policy interventions that align with the most impactful SDGs. Therefore, access to SDG7 should be improved as follows: (i) The government should implement regulatory frameworks, such as feed-in tariffs, tax incentives, and direct subsidies, to promote investment in solar, wind, and hydrogen energy. (ii) Public and private partnerships should be strengthened to accelerate the deployment of smart grids and energy storage technologies, ensuring a reliable energy supply. (iii) Expanding renewable solutions off-grid in rural areas of Ha’il can bridge energy access gaps and promote equitable energy distribution. Then, for SDG12, (i) businesses should integrate energy-efficient technologies and adopt waste-to-energy solutions to minimize environmental impact. (ii) Regulatory authorities must enforce stricter industrial energy consumption policies while promoting eco-labeling and certification programs to encourage sustainable consumer behavior. (iii) Educational institutions should conduct awareness campaigns on energy conservation, resource efficiency, and sustainable production practices. Regarding strengthening SDG13: (i) The Ha’il region should prioritize reducing carbon emissions by phasing out fossil fuel subsidies and introducing carbon pricing mechanisms. (ii) The authorities of Ha’il should encourage green infrastructure projects to strengthen climate resilience. Similarly, large-scale investments in this area can help offset carbon emissions. (iii) Promoting corporate sustainability policies and climate-conscious behaviors through education can facilitate a transition toward a low-carbon future. Finally, a successful renewable energy transition in Ha’il requires a multi-stakeholder approach that incorporates effective policies, private sector investments, and community engagement. By implementing these targeted recommendations, the region can accelerate its transition to renewable energy, ensuring economic growth, environmental sustainability, and long-term energy security.

Limitations: This study is constrained by the availability and temporal frequency of regional data, which can affect the granularity of the analysis. Furthermore, the emphasis on selected key indicators may overlook other pertinent factors that affect the adoption of renewable energy. Nevertheless, these limitations are inherent to the unique characteristics of the Ha’il region, underscoring the distinctiveness and contribution of this research.

Future research directions: To further enrich the findings, future research should investigate the long-term economic and ecological impacts of renewable energy strategies. This involves examining direct and indirect effects on the local economy, employment, and sustainability of the ecosystem. Longitudinal studies can track trends in the adoption of energy technologies, energy consumption patterns, and changes in energy demand over time. Additionally, comparative studies between different regions can offer broader insights into best practices and the effectiveness of policies in promoting the adoption of renewable energy.

Author Contributions: Conceptualization, Methodology, and Software: Y.B., R.T. and M.M.B.; Resources: R.T. and S.M.M.; Data curation: R.T.; Writing—original draft: Y.B., R.T., M.M.B. and S.M.M. All authors have read and agreed to the published version of the manuscript.

Funding: This research has been funded by the Scientific Research Deanship at the University of Ha’il—Saudi Arabia through project number $" \mathrm { R C P } - 2 4 0 0 8 ^ { \prime \prime }$ .

Institutional Review Board Statement: Not applicable.

Informed Consent Statement: Not applicable.

Data Availability Statement: The datasets used during the current study are available from the corresponding or first author on reasonable request.

Conflicts of Interest: The authors declare no conflict of interest.

# References

1. Hassan, Q.; Viktor, P.; Al-Musawi, T.J.; Ali, B.M.; Algburi, S.; Alzoubi, H.M.; Al-Jiboory, A.K.; Sameen, A.Z.; Salman, H.M.; Jaszczur, M. The renewable energy role in the global energy Transformations. Renew. Energy Focus 2024, 48, 100545. [CrossRef]   
2. Gayen, D.; Chatterjee, R.; Roy, S. A review on environmental impacts of renewable energy for sustainable development. Int. J. Environ. Sci. Technol. 2024, 21, 5285–5310. [CrossRef]   
3. Chen, C.; Li, W.B.; Zheng, L.; Guan, C. Exploring the impacts of spatial regulation on environmentally sustainable development: A new perspective of quasi–experimental evaluation based on the National Key Ecological Function Zones in China. Sustain. Dev. 2024, 32, 404–424. [CrossRef]   
4. Laaroussi, A.; Laaroussi, O.; Bouayad, A. Environmental impact study of the NOOR 1 solar project on the Southern Region of Morocco. Renew. Energy Environ. Sustain. 2023, 8, 9. [CrossRef]   
5. Kamboj, P.; Hejazi, M.; Qiu, Y.; Kyle, P.; Iyer, G. The path to 2060: Saudi Arabia’s long-term pathway for GHG emission reduction. Energy Strategy Rev. 2024, 55, 101537. [CrossRef]   
6. Nassar, K. Strategic energy transition in the Gulf Cooperation Council: Balancing economic, social, political, and environmental dynamics for sustainable development. Int. J. Green Energy 2024, 22, 1570–1586. [CrossRef]   
7. Farahat, A.; Kambezidis, H.D.; Labban, A. The Solar Radiation Climate of Saudi Arabia. Climate 2023, 11, 75. [CrossRef]   
8. Alanazi, A.; Hassan, I.; Jan, S.T.; Alanazi, M. Multi-criteria analysis of renewable energy technologies performance in diverse geographical locations of Saudi Arabia. Clean Technol. Environ. Policy 2024, 26, 1165–1196. [CrossRef]   
9. Khan, M.A.; Cárdenas-Barrón, L.E.; Treviño-Garza, G. Strategizing emissions reduction investment for a livestock production farm amid power demand pattern: A path to sustainable growth under the carbon cap environmental regulation. Oper. Res. Perspect. 2024, 13, 100313. [CrossRef]   
10. GIROUD. World Investment Report 2023: Investing in sustainable energy for all. In Proceedings of the United Nations Conference on Trade and Development, Geneva, Switzerland, 8–9 May 2023; GIROUD: New York, NY, USA, 2024. 205p.   
11. Zhang, X.; Khan, K.; Shao, X.; Oprean-Stan, C.; Zhang, Q. The rising role of artificial intelligence in renewable energy development in China. Energy Econ. 2024, 132, 107489. [CrossRef]   
12. Sami, U.; Lin, B.; Zhu, R. Developing a new sustainable policy perspective for Pakistan: An intricate nexus between green innovation. financial structure and ecological footprint. Energy 2025, 316, 134496.   
13. Ahmed, S.; Ali, A.; D’Angola, A. A Review of Renewable Energy Communities: Concepts, Scope, Progress, Challenges, and Recommendations. Sustainability 2024, 16, 1749. [CrossRef]   
14. Storey, A.; Johnson, D.; Chatellier, J.; Participation, C. Experience, and Perceptions of the Solar Over Louisville Solarize Program; A Technical Report Prepared for Louisville Metro Government; Louisville Metro Government: Louisville, KY, USA, 2023.   
15. Aldulaimi, S.H.; Abdeldayem, M.M. Examining the impact of renewable energy technologies on sustainability development in the middle east and north Africa region. Int. J. Eng. Bus. Manag. 2022, 14, 18479790221110835. [CrossRef]   
16. Magadum, S.; Prasad, P.; Almeida, J. Forecasting Renewable Energy Generation Capacity in South America by 2050 Using Machine Learning Models. arXiv 2025, arXiv:2503.17771.   
17. Nguyen, H.; Tran, D.; Li, Y. A Comparative Sustainability Assessment of Energy Transitions in Southeast Asia Using the SEEN Framework. Energies 2025, 18, 287.   
18. Dahhou, K.; Ezzahid, E.; Bouzidi, A. The Impact of Renewable Energy Consumption on Economic Growth in Morocco. Int. J. Energy Econ. Policy 2025, 15, 33–40. [CrossRef]   
19. Benchikh, S.; El Amrani, M.; Tazi, S. Harnessing Morocco’s Renewable Potential: Wind and Solar Energy Development. SCT Proc. Interdiscip. Insights Innov. 2024, 2, 318.   
20. Cheng, C.; Fang, Z.; Zhou, Q.; Jiang, Y.; Xue, S.; Zhao, S.; Wang, W.; Zhuang, Y.; Ding, T.; Tang, Y.; et al. Nature’s hand in megacity cluster progress: Integrating SDG11 with ecosystem service dynamics. Sustain. Cities Soc. 2024, 108, 105471. [CrossRef]   
21. Garcia, L.B.; Fiore, F.A.; Carvalho, F.L.C. Factors associated with the use of solar energy in urban households: Case study: Municipality of são josé dos campos. Int. J. Energy Econ. Policy 2023, 13, 522–530. [CrossRef]   
22. Patel, R.K.; Jha, P.; Chauhan, A.; Kant, R.; Kumar, R. Polycyclic Pyrazoles from Alkynyl Cyclohexadienones and Nonstabilized Diazoalkanes via $[ 3 + 2 ]$ -Cycloaddition/[1, 5]-Sigmatropic Rearrangement/Aza-Michael Reaction Cascade. Org. Lett. 2024, 26, 839–844. [CrossRef]   
23. Lee, D.Y.; Park, S.Y. Global energy intensity convergence using a spatial panel growth model. Appl. Econ. 2022, 55, 4745–4764. [CrossRef]   
24. Abhishek, N.; Rahiman, H.U.; Suraj, N.; Abhinandan, K.; Ashoka, M.L.; Divyashree, M.S.; Raghupathif, S. Renewable energy initiatives by corporates and sustainable development–a mediation analysis. Cogent Econ. Financ. 2024, 12, 2344122.   
25. Lafortune, G.; Sachs, J.D. The Index of Countries’ Support for UN-based Multilateralism: Construction, Verification, and Correlates. Asian Econ. Pap. 2024, 23, 1–28. [CrossRef]   
26. Triki, R.; Maâloul, M.H.; Bahou, Y.; Kadria, M. The Impact of Digitization to Ensure Competitiveness of the Ha’il Region to Achieve Sustainable Development Goals. Sustainability 2023, 15, 1661. [CrossRef]   
27. Ordonez-Ponce, E. The role of local cultural factors in the achievement of the sustainable development goals. Sustain. Dev. 2023, 31, 1122–1134. [CrossRef]   
28. Tian, M.; Liu, Y.; Zhang, S.; Yu, C.; Ostrikov, K.; Zhang, Z. Overcoming the permeability-selectivity challenge in water purification using two-dimensional cobalt-functionalized vermiculite membrane. Nat. Commun. 2024, 15, 391. [CrossRef]   
29. Agbakwuru, V.; Obidi, P.O.; Salihu, O.S.; MaryJane, O.C. The role of renewable energy in achieving sustainable development goals. Int. J. Eng. Res. Updates 2024, 7, 13–027. [CrossRef]   
30. Krauss, J.E.; Cisneros, A.J.; Requena-I-Mora, M. Mapping Sustainable Development Goals 8, 9, 12, 13 and 15 through a decolonial lens: Falling short of ‘transforming our world’. Sustain. Sci. 2022, 17, 1855–1872. [CrossRef]   
31. Raman, R.; Lathabai, H.H.; Nedungadi, P. Sustainable development goal 12 and its synergies with other SDGs: Identification of key research contributions and policy insights. Discov. Sustain. 2024, 5, 150. [CrossRef]   
32. Dickey, D.A.; Fuller, W.A. Distribution of the Estimators for Autoregressive Time Series with a Unit Root. J. Am. Stat. Assoc. 1979, 74, 427–431.   
33. Phillips, P.C.B.; Perron, P. Testing for a Unit Root in Time Series Regression. Biometrika 1988, 75, 335–346. [CrossRef]   
34. Johansen, S. Statistical Analysis of Cointegration Vectors. J. Econ. Dyn. Control 1988, 12, 231–254. [CrossRef]   
35. Johansen, S. Estimation and Hypothesis Testing of Cointegration Vectors in Gaussian Vector Autoregressive Models. Econometrica 1991, 59, 1551–1580. [CrossRef]   
36. Ban, R.; Deng, Y. Optimization Method for Predictive Models Based on ARIMA Time Series and K-means Clustering Algorithm. In Proceedings of the 2024 36th Chinese Control and Decision Conference (CCDC), Xi’an, China, 25–27 May 2024; pp. 2391–2396. [CrossRef]   
37. Win, H.H.; San, K.K.; Chaw, K.E.E. Deep Learning for Commodity Price Forecasting: LSTM, BiLSTM, GRU Comparison with Variable Window Sizes and Activation Functions. In Proceedings of the 5th International Conference on Advanced Information Technologies (ICAIT), Yangon, Myanmar, 7 November 2024; pp. 1–6. [CrossRef]

Disclaimer/Publisher’s Note: The statements, opinions and data contained in all publications are solely those of the individual author(s) and contributor(s) and not of MDPI and/or the editor(s). MDPI and/or the editor(s) disclaim responsibility for any injury to people or property resulting from any ideas, methods, instructions or products referred to in the content.